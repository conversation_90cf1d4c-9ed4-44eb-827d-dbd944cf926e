<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <div class="container mt-1">
        <el-form v-model="searchFrom" inline size="small">
          <el-form-item :label="$t('order.search.orderNo') + '：'">
            <el-input
              v-model="searchFrom.orderNo"
              :placeholder="$t('order.search.orderNo')"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('order.search.productTitle') + '：'">
            <el-input
              v-model="searchFrom.productTitle"
              :placeholder="$t('order.search.productTitle')"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('order.search.status') + '：'">
            <el-select
              v-model="searchFrom.status"
              :placeholder="$t('common.all')"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="$t('order.search.' + item.label)"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-button size="small" type="primary" class="mr10" @click="getList(1)">
        {{ $t("common.query") }}
      </el-button>

      <el-button size="small" type="" class="mr10" @click="resetForm">
        {{ $t("common.reset") }}
      </el-button>
    </el-card>

    <el-card class="box-card" style="margin-top: 12px">
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          :label="$t('common.serialNumber')"
          type="index"
          width="110"
        ></el-table-column>
        <el-table-column
          :label="$t('order.search.image')"
          min-width="80"
        >
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="scope.row.avatar"
                :preview-src-list="[scope.row.avatar]"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('order.search.orderId')"
          min-width="80"
          prop="id"
        ></el-table-column>
        <el-table-column
          :label="$t('chainTransferRecord.nickname')"
          min-width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.realName | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('order.search.orderNo')"
          min-width="80"
          prop="orderId"
        ></el-table-column>
        <el-table-column
          :label="$t('order.search.productName')"
          width="120"
          prop="productName"
        ></el-table-column>
        <el-table-column
          :label="$t('order.search.payCount')"
          min-width="80"
          prop="payCount"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.payCount | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('order.search.actualCommission')"
          width="120"
          prop="price"
        ></el-table-column>
        <el-table-column
          :label="$t('order.search.payPrice')"
          width="120"
          prop="totalPrice"
        ></el-table-column>
        <el-table-column :label="$t('order.search.commissionRate')" width="80">
          <template slot-scope="scope">{{
            formatRate(scope.row.commissionRate)
          }}</template>
        </el-table-column>
        <!-- 隐藏预计返现金额列 -->
        <!-- <el-table-column
          :label="$t('order.search.estimatedCommission')"
          width="140"
          prop="estimatedCommission"
        ></el-table-column> -->
        <!-- 隐藏用户返现率列 -->
        <!-- <el-table-column
          :label="$t('order.search.userCashBackRate')"
          width="120"
        >
          <template slot-scope="scope">{{
            formatRate(scope.row.userCashBackRate)
          }}</template>
        </el-table-column> -->
        <el-table-column
          :label="$t('order.search.creatTime')"
          width="120"
          prop="createTime"
        ></el-table-column>
        <el-table-column
          :label="$t('order.search.contentId')"
          min-width="80"
          prop="contentId"
        ></el-table-column>
        <el-table-column
          :label="$t('order.search.statusLabel')"
          min-width="80"
        >
          <template slot-scope="scope">{{
              $t(`order.search.${scope.row.statusCode}`)
            }}</template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="mt20"
        @size-change="sizeChange"
        @current-change="pageChange"
        :current-page="searchFrom.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="searchFrom.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="searchFrom.total"
      >
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { orderListApi } from "@/api/order";
export default {
  name: "OrderSearch",
  data () {
    return {
      loading: false,
      searchFrom: {
        orderNo: "",
        productTitle: "",
        type: "2",
        dateLimit: "",
        page: 1,
        limit: 20,
        total: 0
      },
      tableData: [],
      statusList: [
        { value: "", label: "all" },
        { value: "unPaid", label: "unPaid" },
        { value: "notShipped", label: "notShipped" },
        { value: "spike", label: "spike" },
        { value: "bargain", label: "bargain" },
        { value: "complete", label: "complete" },
        { value: "toBeWrittenOff", label: "toBeWrittenOff" },
        { value: "refunding", label: "refunding" },
        { value: "refunded", label: "refunded" },
        { value: "deleted", label: "deleted" }
      ]
    };
  },
  created () { },
  mounted () {
    this.getList();
  },
  methods: {
    // 列表
    getList (num) {
        let _this = this;
      this.loading = true;

      this.searchFrom.page = num ? num : this.searchFrom.page;
      orderListApi(this.searchFrom)
        .then(res => {
          this.tableData = res.list || [];
          this.tableData.forEach(item => {
            item.payCount = item.productList ? item.productList.length : 0;
            // item.statusLabel = this.statusList.filter(
            //   i => i.value == item.status
            // )[0].label;

            // 安全检查：确保productList存在且不为空
            if (item.productList && item.productList.length > 0) {
              item.productName = item.productList[0].productName;
              item.actualCommission = item.productList[0].actualCommission;
              item.commissionRate = item.productList[0].commissionRate;
              item.contentId = item.productList[0].contentId;
              item.estimatedCommission = item.productList[0].estimatedCommission;
              item.price = _this.formatAmount(item.productList[0].price)
              item.image = item.productList[0].image
              item.avatar = item.productList[0].image  // 设置avatar字段用于模板显示
            } else {
              // 设置默认值
              item.productName = '';
              item.actualCommission = 0;
              item.commissionRate = 0;
              item.contentId = '';
              item.estimatedCommission = 0;
              item.price = _this.formatAmount(0);
              item.image = '';
              item.avatar = '';  // 设置默认avatar值
            }

            item.totalPrice = _this.formatAmount(item.totalPrice)

          });
          this.searchFrom.total = res.total || 0;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.tableData = [];
          this.searchFrom.total = 0;
        });
    },
    formatAmount(s){
        if(s == undefined) {
            s = 0
        }
        let s1 = (s/1000).toFixed(3)
        return s1
    },
    //切换页数
    pageChange (index) {
      this.searchFrom.page = index;
      this.getList();
    },
    //切换显示条数
    sizeChange (index) {
      this.searchFrom.limit = index;
      this.getList();
    },
    resetForm () {
      this.searchFrom = {
        orderNo: "",
        productTitle: "",
        type: "2",
        dateLimit: "",
        page: 1,
        limit: 20,
        total: 0
      };
      this.getList();
    },
    formatRate (s) {
        if(s == undefined) {
            s = 0
        }
         return parseInt(s * 10000) / 100 + "%";
    }
  }
};
</script>
<style scoped lang="scss">
/**/
</style>
