PODS:
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - tiktok_sdk_v2 (0.0.1):
    - Flutter
    - TikTokOpenAuthSDK (~> 2.5.0)
    - TikTokOpenSDKCore (~> 2.5.0)
    - TikTokOpenShareSDK (~> 2.5.0)
  - TikTokOpenAuthSDK (2.5.0):
    - TikTokOpenAuthSDK/Auth (= 2.5.0)
    - TikTokOpenSDKCore (= 2.5.0)
  - TikTokOpenAuthSDK/Auth (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
  - TikTokOpenSDKCore (2.5.0):
    - TikTokOpenSDKCore/Core (= 2.5.0)
  - TikTokOpenSDKCore/Core (2.5.0)
  - TikTokOpenShareSDK (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
    - TikTokOpenShareSDK/Share (= 2.5.0)
  - TikTokOpenShareSDK/Share (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - tiktok_sdk_v2 (from `.symlinks/plugins/tiktok_sdk_v2/ios`)
  - TikTokOpenAuthSDK
  - TikTokOpenSDKCore
  - TikTokOpenShareSDK
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - TikTokOpenAuthSDK
    - TikTokOpenSDKCore
    - TikTokOpenShareSDK

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  tiktok_sdk_v2:
    :path: ".symlinks/plugins/tiktok_sdk_v2/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  tiktok_sdk_v2: fda5bc4c8f1dc464ffc7a27e33f2e8901c347994
  TikTokOpenAuthSDK: 35d99f5778b9635ab983bb25c4acf6ccad4404a9
  TikTokOpenSDKCore: e6f34e48bd6e85e4d94f9c04782c13d5defafb55
  TikTokOpenShareSDK: a7da017bc66c28d0aefea9342c0cfcc7e52ea2b7
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: d136785f2e14359d1baf5cebe9fe220cbc78733b

COCOAPODS: 1.16.2
