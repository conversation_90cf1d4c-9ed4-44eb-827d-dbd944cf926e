// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Indonesian (`id`).
class SId extends S {
  SId([String locale = 'id']) : super(locale);

  @override
  String get home_navigation_home => 'Beranda';

  @override
  String get home_navigation_brand => 'Brand';

  @override
  String get home_navigation_income => 'Pendapatan';

  @override
  String get home_navigation_mine => 'Saya';

  @override
  String get login_mobile_title => 'Masuk via Nomor Ponsel';

  @override
  String get login_welcome_back => 'Selamat Datang Kembali';

  @override
  String get login_mobile_subtitle => 'Silakan masuk dengan nomor ponsel';

  @override
  String get login_phone_label => 'Nomor Ponsel';

  @override
  String get login_phone_hint => 'Masukkan nomor ponsel';

  @override
  String get login_code_label => 'Kode Verifikasi';

  @override
  String get login_code_hint => 'Masukkan kode verifikasi';

  @override
  String get login_get_code => 'Dapatkan Kode';

  @override
  String get login_code_seconds => 'detik';

  @override
  String get login_button => 'Masuk';

  @override
  String get login_password_alternative => 'Masuk dengan Sandi';

  @override
  String get login_enter_phone => 'Harap masukkan nomor ponsel';

  @override
  String get login_code_sent => 'Kode verifikasi telah dikirim';

  @override
  String get login_send_failed => 'Pengiriman kode gagal';

  @override
  String get login_enter_phone_code => 'Masukkan nomor ponsel dan kode';

  @override
  String get login_failed => 'Gagal masuk';

  @override
  String get error_title => 'Maaf! Terjadi kesalahan. Silakan coba lagi.';

  @override
  String get error_button_title => 'Coba Lagi';

  @override
  String get loading_more_error => 'Gagal memuat, silakan coba lagi';

  @override
  String get loading_more_no_more => 'Sudah sampai akhir~';

  @override
  String get loading_more_empty => 'Belum ada wallpaper ~';

  @override
  String get loading_more_write => 'Tulis Ulasan';

  @override
  String get loading_more_retry => 'Coba Lagi';

  @override
  String get home_rebate_rate_title => 'Persentase Cashback';

  @override
  String get home_platform_all => 'Semua';

  @override
  String get home_platform_hot_sale => 'Terlaris';

  @override
  String get home_platform_high_rebate => 'Cashback Tinggi';

  @override
  String get home_platform_tiktok => 'TikTok';

  @override
  String get home_platform_shopee => 'Shopee';

  @override
  String get home_logo_slogan => 'Cari & Bandingkan, Dapat Cashback!';

  @override
  String get home_search_placeholder =>
      'Salin tautan produk, dapatkan cashback';

  @override
  String get home_search_button_title => 'Tempel';

  @override
  String get home_search_instructions_copy => 'Salin';

  @override
  String get home_search_instructions_text =>
      'Buka tautan di GENCO, raih cashback';

  @override
  String get home_cashback_instructions_title => 'Cara Dapatkan Cashback';

  @override
  String get home_cashback_instructions_check_all => 'Lihat Semua';

  @override
  String get home_cashback_instructions_step1 => 'Salin\ntautan produk';

  @override
  String get home_cashback_instructions_step2 => 'Buka GENCO\nCek cashback';

  @override
  String get home_cashback_instructions_step3 => 'Beli di\nShopee/TikTok';

  @override
  String get home_cashback_instructions_step4 => 'Dapatkan Cashback';

  @override
  String get home_cashback_button_title => 'Perkiraan jumlah cashback';

  @override
  String get detail_price_title => 'Harga';

  @override
  String get detail_sold => 'Terjual';

  @override
  String get detail_sold_count => 'pcs';

  @override
  String get detail_cashback_amount => 'Perkiraan Cashback';

  @override
  String get detail_rebate_rate => 'Persentase Cashback';

  @override
  String get detail_cashback_flow_title => 'Alur Dapatkan Cashback';

  @override
  String get detail_cashback_flow_check => 'Lihat Panduan';

  @override
  String get detail_cashback_flow_step1 => 'Klik produk';

  @override
  String get detail_cashback_flow_step2 => 'Pesan via\nTikTok';

  @override
  String get detail_cashback_flow_step3 => 'Lacak Pesanan';

  @override
  String get detail_cashback_flow_step4 => 'Dapatkan\nCashback';

  @override
  String get detail_brand_product_amount_pre => 'Total';

  @override
  String get detail_brand_product_amount => 'produk';

  @override
  String get brand_filter_all => 'Semua';

  @override
  String get brand_filter_price => 'Harga';

  @override
  String get brand_filter_rebate_rate => 'Cashback';

  @override
  String get brand_filter_sales => 'Penjualan';

  @override
  String get brand_filter_latest => 'Baru';

  @override
  String get usage_guideline_title => 'Panduan Pengguna';

  @override
  String get usage_guideline_description =>
      'Dapatkan cashback di Shopee/TikTok dalam 3 langkah mudah!';

  @override
  String get usage_guideline_step1 =>
      'Buka produk di <red>Shopee</red> atau <red>TikTok</red>, salin tautan produk';

  @override
  String get usage_guideline_step2 =>
      'Buka <red>GENCO</red>, tempel tautan, lihat cashback';

  @override
  String get usage_guideline_step3 =>
      'Klik ke <red>Shopee</red>/<red>TikTok</red> via GENCO, selesaikan pembelian';

  @override
  String get brand_home_top_title => 'Brand Pilihan <red>Premium</red>';

  @override
  String get brand_home_top_subtitle =>
      'Temukan favoritmu dengan harga terbaik & cashback!';

  @override
  String get brand_tiktok_hot_sale_title => 'Brand Populer di TikTok';

  @override
  String get brand_high_rebate_title => 'Brand Cashback Tinggi';

  @override
  String get brand_highest_rebate_rate => 'Cashback Tertinggi';

  @override
  String get message_no_data => 'Belum ada data';

  @override
  String get income_pre_total_income => 'Pendapatan Diterima';

  @override
  String get income_today => 'Pendapatan Hari Ini';

  @override
  String get income_amount_to_be_credited => 'Menunggu Pembayaran';

  @override
  String get income_amount_to_be_credited_hint =>
      'Termasuk cashback pesanan yang belum dikonfirmasi. Hanya perkiraan.';

  @override
  String get income_amount_credited => 'Dana Masuk';

  @override
  String get income_amount_credited_description => 'Jumlah yang sudah diterima';

  @override
  String get income_amount_available_for_withdrawal =>
      'Saldo yang dapat ditarik';

  @override
  String get income_withdrawal_button => 'Tarik Dana';

  @override
  String get income_withdrawal_success => 'Penarikan Berhasil!';

  @override
  String get income_withdrawal_failed => 'Penarikan Gagal';

  @override
  String get income_withdrawal_amount => 'Jumlah Penarikan';

  @override
  String get income_withdrawal_amount_hint => 'Masukkan jumlah penarikan';

  @override
  String get income_transaction_history => 'Riwayat Transaksi';

  @override
  String get income_transaction_history_empty => 'Belum ada transaksi';

  @override
  String get income_transaction_detail => 'Detail Transaksi';

  @override
  String get income_my_order => 'Pesanan Saya';

  @override
  String get income_income_detail => 'Detail Pendapatan';

  @override
  String get income_pre_total_income_description =>
      'Perkiraan total pendapatan sebagai referensi. Jumlah akhir akan disesuaikan dengan pembayaran aktual';

  @override
  String get ok => 'OK';

  @override
  String get finish => 'Selesai';

  @override
  String get credited_rebase_income => 'Cashback Diterima';

  @override
  String get all => 'Semua';

  @override
  String get income => 'Pemasukan';

  @override
  String get expenditure => 'Pengeluaran';

  @override
  String get rebase_income => 'Cashback Masuk';

  @override
  String get rebase_expenditure => 'Cashback Keluar';

  @override
  String get withdrawal_account => 'Akun Penarikan';

  @override
  String get please_select_withdrawal_account => 'Pilih akun penarikan';

  @override
  String get withdrawal_amount => 'Jumlah maksimum yang bisa ditarik';

  @override
  String get withdrawal_amount_hint => 'Jumlah minimum yang bisa ditarik';

  @override
  String get withdrawal_amount_min => 'Minimal penarikan';

  @override
  String get withdrawal_all => 'Tarik Semua';

  @override
  String get withdrawal_finish => 'Penarikan Selesai';

  @override
  String get withdrawal_success => 'Penarikan berhasil diajukan!';

  @override
  String get withdrawal_success_hint =>
      'Dana masuk ≤24 jam kerja. Cek saldo akun Anda!';

  @override
  String get withdrawal_failed => 'Penarikan Gagal';

  @override
  String get withdrawal_fees => 'Biaya Admin';

  @override
  String get withdrawal_fees_hint =>
      'Biaya 1.5% dari penarikan. Minimal Rp5.550. Jika <Rp5.550, biaya tetap Rp5.550.';

  @override
  String get withdrawal_hint => 'Catatan';

  @override
  String get withdrawal_hint_description =>
      'Dana masuk ≤24 jam kerja (selain Sabtu/Minggu/libur)';

  @override
  String get trade_type => 'Jenis Transaksi';

  @override
  String get trade_time => 'Waktu';

  @override
  String get trade_serial_number => 'Nomor Transaksi';

  @override
  String get trade_channel => 'Channel';

  @override
  String get trade_order_number => 'ID Pesanan';

  @override
  String get income_order_rebate => 'Cashback Pesanan';

  @override
  String get income_campaign_reward => 'Hadiah Event';

  @override
  String get income_expected_total_amount => 'Total Estimasi Cashback';

  @override
  String get income_expected_total_amount_hint =>
      'Estimasi cashback pesanan dibayar. Dapat berubah jika ada refund.';

  @override
  String get income_actual_credited_amount => 'Jumlah Cashback Sebenarnya';

  @override
  String get income_actual_credited_amount_hint => 'Sudah Masuk ke Akun';

  @override
  String get order_title => 'Pesanan Saya';

  @override
  String get order_tab_all => 'Semua';

  @override
  String get order_tab_processing => 'Diproses';

  @override
  String get order_tab_completed => 'Selesai';

  @override
  String get order_tab_expired => 'Kadaluarsa';

  @override
  String get order_application_time => 'Waktu permintaan:';

  @override
  String get order_status_processing => 'Diproses';

  @override
  String get order_status_completed => 'Selesai';

  @override
  String get order_status_expired => 'Kadaluarsa';

  @override
  String get order_expected_cashback => 'Estimasi cashback:';

  @override
  String get order_cashback_info => 'Ketentuan Cashback';

  @override
  String get rebate_instruction_title => 'Syarat Cashback';

  @override
  String get rebate_instruction_content =>
      'Cashback masuk ke saldo GENCO setelah pesanan dikonfirmasi di Shopee/TikTok. Proses verifikasi: 1-3 hari.';

  @override
  String get rebate_step_1_title => '01';

  @override
  String get rebate_step_1_content =>
      'Pesan via Shopee/TikTok lewat link GENCO.';

  @override
  String get rebate_step_2_title => '02';

  @override
  String get rebate_step_2_content => 'GENCO verifikasi otomatis (1-3 hari).';

  @override
  String get rebate_step_3_title => '03';

  @override
  String get rebate_step_3_content => 'Cashback masuk setelah pesanan SELESAI.';

  @override
  String get rebate_how_to_get_title => 'Cara Dapatkan Cashback:';

  @override
  String get rebate_how_to_order_title => 'CARA BELI';

  @override
  String get rebate_how_to_order_content =>
      'Setiap produk HARUS diakses lewat GENCO sebelum checkout.';

  @override
  String get rebate_genco_last_app_title => 'GENCO HARUS App Terakhir';

  @override
  String get rebate_genco_last_app_content =>
      'Cashback tidak dapat dilacak jika membuka aplikasi lain setelah GENCO';

  @override
  String get rebate_unsupported_order_title => 'Pesanan Tidak Memenuhi Syarat';

  @override
  String get rebate_unsupported_order_content =>
      'Pesanan di luar tautan GENCO tidak memenuhi syarat.';

  @override
  String get rebase_info => 'Detail Cashback';

  @override
  String get rebase_price => 'Harga Beli';

  @override
  String get rebase_rate => 'Persentase Cashback';

  @override
  String get rebase_cash => 'Cashback yang Diperoleh';

  @override
  String get withdrawal_choose_method => 'Pilih Akun Penarikan';

  @override
  String get withdrawal_add_card => '+ Rekening Bank';

  @override
  String get withdrawal_add_e_card => '+ Tambah E-Wallet';

  @override
  String get name => 'Nama';

  @override
  String get name_placeholder => 'Nama sesuai rekening';

  @override
  String get bank_name => 'Nama Bank';

  @override
  String get select_bank => 'Pilih Bank';

  @override
  String get bank_card_number => 'Nomor Rekening';

  @override
  String get input_bank_card_number => 'Masukkan nomor rekening';

  @override
  String get search => 'Cari';

  @override
  String get button_next => 'Lanjut';

  @override
  String get bind_bank_card_confirm => 'Konfirmasi Kartu';

  @override
  String get e_wallet => 'E-Wallet';

  @override
  String get phone_number => 'Nomor Telepon';

  @override
  String get phone_number_placeholder => 'Masukkan Nomor Telepon';

  @override
  String get select_e_wallet => 'Pilih E-Wallet';

  @override
  String get usage_hint => 'Hemat dengan GENCO!';

  @override
  String get my_collection => 'Favorit Saya';

  @override
  String get guide_step1_title => '4 Langkah Dapat Cashback';

  @override
  String get guide_step1_content_flow_1 =>
      'Buka Shopee/TikTok, salin tautan produk';

  @override
  String get guide_step1_content_flow_2 => 'Buka GENCO, cek cashback';

  @override
  String get guide_step1_content_flow_3 => 'Klik ke Shopee/TikTok, beli';

  @override
  String get guide_step1_content_flow_4 => 'Cashback masuk setelah selesai';

  @override
  String get guide_step1_content_flow_1_title => 'SALIN TAUTAN PRODUK';

  @override
  String get guide_step1_content_flow_1_description =>
      'Di Shopee/TikTok: Klik \'Bagikan\' > \'Salin Tautan\'.';

  @override
  String get guide_step1_content_flow_2_title => 'CEK CASHBACK DI GENCO';

  @override
  String get guide_step1_content_flow_2_description =>
      'Buka GENCO > Tempel otomatis atau ketik manual.';

  @override
  String get guide_step1_content_flow_3_title => 'BELI VIA GENCO';

  @override
  String get guide_step1_content_flow_3_description =>
      'Klik \'Beli di Shopee/TikTok\' > Checkout seperti biasa.';

  @override
  String get guide_step1_content_flow_4_title => 'DAPATKAN CASHBACK';

  @override
  String get guide_step1_content_flow_4_description =>
      'Cashback otomatis masuk setelah klik \'Pesanan Selesai\' di Shopee/TikTok.';

  @override
  String get guide_step1_content_flow_5_title => 'Panduan Video';

  @override
  String get guide_step1_content_flow_5_description =>
      'Tonton video cara klaim cashback!';

  @override
  String get feedback_cash => 'Feedback Cashback';

  @override
  String get edit => 'Edit';

  @override
  String get cancel => 'Batal';

  @override
  String get select_all => 'Pilih Semua';

  @override
  String get cancel_select_all => 'Batalkan Pilihan';

  @override
  String get delete => 'Hapus';

  @override
  String get setting => 'Pengaturan';

  @override
  String get my_avatar => 'Foto Profil';

  @override
  String get nickname => 'Nama Panggilan';

  @override
  String get whatsapp_account => 'Akun WhatsApp';

  @override
  String get modify_phone_number => 'Ubah Nomor Ponsel';

  @override
  String get modify_password => 'Ubah Kata Sandi';

  @override
  String get privacy => 'Privasi';

  @override
  String get about => 'Tentang';

  @override
  String get modify_nickname => 'Ubah Nickname';

  @override
  String get login_with_tiktok => 'Masuk dengan TikTok';

  @override
  String get login_title => 'Selamat Datang di GENCO';

  @override
  String get login_subtitle => 'Beli Hemat,\nBagikan Dapat Duit';

  @override
  String get whatsapp_account_hint => 'Masukkan nomor WhatsApp Anda';

  @override
  String get next_step => 'Lanjut';

  @override
  String get account_empty_hint => 'Nomor WhatsApp tidak boleh kosong';

  @override
  String get input_opt_verification_code => 'Masukkan Kode OTP';

  @override
  String input_opt_verification_code_hint(String phone) {
    return 'Kode verifikasi dikirim ke WhatsApp $phone';
  }

  @override
  String get login_with_password => 'Masuk dengan Sandi';

  @override
  String get resend_in => 'Kirim Ulang dalam';

  @override
  String get seconds => ' detik';

  @override
  String get resend_code => 'Kirim Ulang';

  @override
  String get login => 'Masuk';

  @override
  String get input_opt_verification_code_error => 'Harap masukkan kode OTP';

  @override
  String get login_success => 'Berhasil Masuk';

  @override
  String get welcome_back => 'Hai, selamat datang kembali!';

  @override
  String get please_input_your_password => 'Masukkan kata sandi Anda';

  @override
  String get login_password => 'Kata Sandi';

  @override
  String get input_password_hint => 'Kata sandi Anda';

  @override
  String get login_with_verification_code => 'Masuk dengan Kode OTP';

  @override
  String get login_agreement => 'Dengan masuk, Anda menyetujui';

  @override
  String get and => 'dan';

  @override
  String get user_agreement => 'Syarat Penggunaan';

  @override
  String get privacy_policy => 'Kebijakan Privasi';

  @override
  String get setting_login_password => 'Atur Kata Sandi';

  @override
  String get set_password_hint => 'Sandi: 6-20 karakter (huruf + angka)';

  @override
  String get login_password_confirm => 'Konfirmasi Sandi';

  @override
  String get login_password_confirm_hint => 'Masukkan kembali sandi';

  @override
  String get password_not_same => 'Sandi tidak cocok';

  @override
  String get modify_success => 'Berhasil Diubah';

  @override
  String get find_product => 'Temukan Produk';

  @override
  String get check_cash_back => 'Cek Cashback';

  @override
  String get back => 'Kembali';

  @override
  String get can_not_open_link => 'Tautan Tidak Bisa Dibuka';

  @override
  String get collection => 'Favorit';

  @override
  String get order_right_now => 'Beli Sekarang';

  @override
  String get add_to_collection_success => 'Ditambahkan ke Favorit';

  @override
  String get please_select_bank_or_e_wallet => 'Pilih Bank/Dompet Digital';

  @override
  String get please_input_amount => 'Masukkan jumlah penarikan';

  @override
  String get please_select_bank => 'Pilih bank';

  @override
  String get please_input_bank_number => 'Masukkan nomor rekening';

  @override
  String get please_select_e_wallet => 'Pilih dompet digital';

  @override
  String get please_input_e_wallet_account => 'Masukkan akun dompet digital';

  @override
  String get logout => 'Keluar';

  @override
  String get logout_confirm_title => 'Konfirmasi Keluar';

  @override
  String get logout_confirm_message => 'Yakin ingin keluar dari akun?';

  @override
  String get confirm => 'OK';

  @override
  String get jump_to_tiktok => 'Segera dialihkan ke TikTok';

  @override
  String share_text(String amount) {
    return 'Segera pesan melalui GENCO untuk memperoleh estimasi cashback sebesar Rp$amount.';
  }

  @override
  String get nickname_hint => 'Silakan masukkan nama panggilan';

  @override
  String get member_introduction =>
      'Tingkatkan menjadi agen atau mitra untuk menghasilkan lebih banyak';

  @override
  String get member_level_state => 'Status Level';

  @override
  String get member_status_description =>
      'Anda saat ini belum menjadi agen atau mitra';

  @override
  String get member_level_silver_agent => 'Agen Perak';

  @override
  String get member_level_partner => 'Mitra';

  @override
  String get member_level_silver_agent_fee => 'Biaya Agen Perak';

  @override
  String get member_level_partner_fee => 'Biaya Mitra';

  @override
  String get become_member => 'Menjadi Agen';

  @override
  String get year => 'Tahun';

  @override
  String get delete_account => 'Nonaktifkan Akun';

  @override
  String get delete_account_title => 'Konfirmasi Penghapusan Akun?';

  @override
  String get delete_account_content =>
      'Setelah akun dihapus, semua hak dan manfaat terkait Anda akan hilang secara permanen,\ndan tidak akan bisa masuk lagi ke akun ini.';

  @override
  String get product_link_empty => 'Tidak menemukan produk untuk link tersebut';

  @override
  String get product_link_empty_content =>
      'Tidak ditemukan produk yang sesuai untuk tautan produk ini. Harap periksa apakah tautannya benar, atau coba produk lain.';

  @override
  String get exclusive_benefits => 'Manfaat Eksklusif';

  @override
  String get member_benefits_silver_agent_1 => 'Masa Berlaku';

  @override
  String get member_benefits_silver_agent_1_value => '1 Tahun';

  @override
  String get member_benefits_silver_agent_2 => 'Bonus Undangan';

  @override
  String get member_benefits_silver_agent_2_value =>
      'Undang berhasil, dapatkan hadiah tambahan';

  @override
  String get member_benefits_silver_agent_3 => 'Komisi Belanja Tim';

  @override
  String get member_benefits_silver_agent_3_value =>
      'Belanja tim, Anda dapatkan keuntungan komisi';

  @override
  String get member_benefits_silver_agent_4 => 'Cashback Tambahan';

  @override
  String get member_benefits_silver_agent_4_value =>
      'Cashback lebih tinggi, hingga 50%';

  @override
  String get member_benefits_silver_agent_5 => 'Cashback Tanpa Batas';

  @override
  String get member_benefits_silver_agent_5_value => 'Cashback tanpa batas';

  @override
  String get member_benefits_partner_agent_1 => 'Masa Berlaku';

  @override
  String get member_benefits_partner_agent_1_value => 'Permanen';

  @override
  String get member_benefits_partner_agent_2 => 'Bonus Undangan';

  @override
  String get member_benefits_partner_agent_2_value =>
      'Dapatkan bonus tinggi 10 miliar+ jika berhasil';

  @override
  String get member_benefits_partner_agent_3 => 'Komisi Belanja Tim';

  @override
  String get member_benefits_partner_agent_3_value =>
      'Dapatkan hingga 20% cashback dari setiap cashback jaringan bawah';

  @override
  String get member_benefits_partner_agent_4 => 'Cashback Tambahan';

  @override
  String get member_benefits_partner_agent_4_value =>
      'Cashback lebih tinggi, hingga 100%';

  @override
  String get role => 'Peran';

  @override
  String get benefit => 'Manfaat';

  @override
  String get normal_user => 'Pengguna Biasa';

  @override
  String get normal_user_2_benefit => 'Tidak ada bonus';

  @override
  String get agent => 'Agen';

  @override
  String get member_benefits_silver_agent_2_benefit =>
      'Bisa dapatkan bonus hingga Rp 10.000.000';

  @override
  String get normal_user_3_benefit => 'Tidak ada komisi belanja';

  @override
  String get member_benefits_silver_agent_3_benefit =>
      'Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) ';

  @override
  String get normal_user_4_benefit => 'Tidak ada komisi belanja';

  @override
  String get member_benefits_silver_agent_4_benefit =>
      'Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) ';

  @override
  String get normal_user_5_benefit => 'Tidak ada komisi belanja';

  @override
  String get member_benefits_silver_agent_5_benefit =>
      'Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) ';

  @override
  String get order_payment => 'Pembayaran Pesanan';

  @override
  String get order_price => 'Jumlah Pesanan';

  @override
  String get product_name => 'Nama Produk';

  @override
  String get real_payment_price => 'Jumlah Pembayaran Aktual';

  @override
  String get agent_fee => 'Biaya Agen';

  @override
  String get purchase_right_now => 'Beli Sekarang';

  @override
  String get payment_problem => 'Masalah Pembayaran';

  @override
  String get payment_complete => 'Pembayaran Selesai';

  @override
  String get payment_agreement => 'Baca dan Setujui';

  @override
  String get payment_agreement_link => 'Syarat Pembayaran';

  @override
  String get cashback_is_0 => 'Produk ini tidak ada cashback';

  @override
  String get cashback_is_0_content =>
      'Produk ini tidak ada cashback. Apakah Anda ingin melanjutkan?';

  @override
  String get nickname_too_long =>
      'Nama panggilan terlalu panjang, maksimal 10 karakter';

  @override
  String get check_payment_result => 'Memeriksa Hasil Pembayaran';

  @override
  String get payment_amount => 'Jumlah Pembayaran';

  @override
  String get payment_id => 'Nomor Transaksi Pembayaran';

  @override
  String get payment_method => 'Metode Pembayaran';

  @override
  String get payment_success => 'Pembayaran Sukses';

  @override
  String get payment_failed => 'Pembayaran Gagal';

  @override
  String get level_status => 'Status Level';

  @override
  String get valid_for => 'Berlaku: 1 tahun';

  @override
  String get upgrade_date => 'Tanggal Upgrade: 11 Juni 2025';

  @override
  String get to_gold_progress => 'Progres Menjadi Agen Emas';

  @override
  String get invite_to_upgrade =>
      'Undang 10 teman untuk menjadi Agen Perak atau lebih tinggi';

  @override
  String get silver_agent => 'Agen Perak';

  @override
  String get gold_agent => 'Agen Emas';

  @override
  String get diamond_agent => 'Agen Berlian';

  @override
  String get partner => 'Mitra';

  @override
  String get direct_invite_reward => 'Hadiah Undangan Langsung';

  @override
  String get direct_invite_detail =>
      'Undang 1 agen dan dapatkan hadiah Rp 35.000. Undang 3 orang untuk balik modal!';

  @override
  String get team_purchase_bonus => 'Bonus Belanja Tim';

  @override
  String get team_purchase_detail =>
      'Dapatkan keuntungan 10% dari total belanja tim yang kamu undang langsung';

  @override
  String get team_purchase_detail_gold =>
      'Undangan tidak langsung (tingkat dua): masing-masing dapat terkumpul Rp 15.000; Undangan tidak langsung (tingkat tiga): masing-masing dapat terkumpul Rp 10.000.';

  @override
  String get training => 'Pelatihan';

  @override
  String get training_detail =>
      'Mentor profesional memberikan kursus berkualitas tinggi dan layanan bimbingan menyeluruh';

  @override
  String get extra_cashback => 'Cashback Ekstra';

  @override
  String get extra_cashback_detail_gold =>
      'Tiap kembangkan 10 Agen Emas: Hadiah Rp 300.000';

  @override
  String get extra_cashback_detail =>
      'Nikmati manfaat cashback ekstra selama periode tertentu';

  @override
  String get invite_to_upgrade_empty =>
      'Undang 10 teman baru untuk bergabung menjadi Agen Perak atau level lebih tinggi.\\nAnda akan ter-upgrade status secara otomatis!';

  @override
  String get silver_partner => 'Mitra Perak';

  @override
  String get gold_partner => 'Mitra Emas';

  @override
  String get diamond_partner => 'Mitra Berlian';

  @override
  String get partner_extra_bonus1 =>
      'Tiap kembangkan 10 Mitra Perak: Hadiah Rp 1.000.000';

  @override
  String get partner_extra_bonus2 =>
      'Tiap kembangkan 10 Mitra Emas: Hadiah Rp 2.000.000';

  @override
  String get direct_invite_detail2 =>
      'Tiap undang 1 agen, hadiah Rp 200.000, undang 3 orang balik modal seketika!';

  @override
  String get team_purchase_detail_gold2 =>
      'Undangan tidak langsung (tingkat dua): masing-masing dapat Rp 100.000; Undangan tidak langsung (tingkat tiga): masing-masing dapat Rp 50.000';

  @override
  String get extra_bonus => 'Bonus Ekstra';

  @override
  String get normal_member => 'Member Biasa';

  @override
  String get high_cashback => 'Cashback Tinggi';

  @override
  String get high_cashback_description =>
      'Semakin banyak belanja, semakin banyak hemat';

  @override
  String get no_limit => 'Tanpa Batas';

  @override
  String get no_limit_description => 'Nikmati cashback tanpa batas';

  @override
  String get user_service => 'Layanan Pelanggan';

  @override
  String get user_service_description => 'Layanan pelanggan berkualitas';

  @override
  String get invite_and_eran_bonus => 'Undang dan Dapatkan Bonus';

  @override
  String get invite_code => 'Kode Undangan';

  @override
  String get input_invite_code => 'Masukkan Kode Undangan';

  @override
  String get contact_up => 'Hubungi Atasan';

  @override
  String get congratulation_to_add_group => 'Selamat Anda bergabung dengan';

  @override
  String get group => ' tim';

  @override
  String get my_team => 'Tim Saya';

  @override
  String get task_center => 'Pusat Tugas';

  @override
  String get task_center_title => 'Pusat Tugas';

  @override
  String get task_cash_income => 'Pendapatan Tunai (Rp)';

  @override
  String get task_withdraw => 'Tarik';

  @override
  String get task_withdrawable_amount => 'Jumlah yang Dapat Ditarik';

  @override
  String get task_daily_tasks => 'Daftar Tugas';

  @override
  String get task_invite_reward => 'Hadiah Pesanan Pertama Undangan';

  @override
  String get task_invite_progress => 'Progres Undangan';

  @override
  String get task_order_progress => 'Progres Pesanan';

  @override
  String get task_invite_count => 'Jumlah Undangan';

  @override
  String get task_order_count => 'Jumlah Pesanan';

  @override
  String get task_conditions_met => 'Kondisi Terpenuhi';

  @override
  String get task_conditions_not_met => 'Kondisi Tidak Terpenuhi';

  @override
  String get task_go_claim => 'Klaim';

  @override
  String get task_feature_developing => 'Fitur Sedang Dikembangkan';

  @override
  String get task_developing =>
      'Silakan selesaikan tugas undangan dan pesanan pertama sebelum mengklaim hadiah';

  @override
  String get task_return_cash_welfare => 'Kesejahteraan Uang Kembali';

  @override
  String get task_return_cash_welfare_desc => 'Eksklusif Harian';

  @override
  String get task_view_record => 'Lihat Catatan';

  @override
  String get task_record_title => 'Catatan Tugas';

  @override
  String get task_total_invites => 'Total Undangan';

  @override
  String get task_redeemed_invites => 'Undangan Ditukar';

  @override
  String get task_total_orders => 'Total Pesanan';

  @override
  String get task_redeemed_orders => 'Pesanan Ditukar';

  @override
  String get task_close => 'Tutup';

  @override
  String get task_reward_amount => 'Jumlah Hadiah';

  @override
  String get task_per_completion => 'Per Penyelesaian';

  @override
  String get task_banner_title => 'Cek';

  @override
  String get task_banner_subtitle => 'Pendapatanmu Disini!';

  @override
  String get task_banner_line1 => 'Semakin banyak transaksi dan undang';

  @override
  String get task_banner_line2 => 'teman, semakin banyak juga';

  @override
  String get task_banner_line3 => 'pendapatanmu.';

  @override
  String get invite_bonus => 'Bonus Undangan';

  @override
  String get shopping_bonus => 'Bonus Belanja';

  @override
  String get cumulative_number_of_invitations => 'Jumlah Undangan Kumulatif';

  @override
  String get today => 'Hari Ini';

  @override
  String get invite_agent => 'Undang Agen';

  @override
  String get invite_normal_user => 'Undang Pengguna Biasa';

  @override
  String get silver => 'Perak';

  @override
  String get gold => 'Emas';

  @override
  String get diamond => 'Berlian';

  @override
  String get team_support => 'Kontribusi Tim';

  @override
  String get received_bonus => 'Bonus yang Diterima';

  @override
  String get invite_time => 'Waktu Undangan';

  @override
  String get pre_team_cashback => 'Perkiraan Cashback Kontribusi Tim';

  @override
  String get invite_and_earn_money => 'Undang dan Hasilkan Uang';

  @override
  String get level_up_schedule => 'Jadwal Naik Level';

  @override
  String get level_up_description => 'Penjelasan Naik Level';

  @override
  String get level_up_content_title =>
      'Undang 10 teman menjadi Agen Perak atau level lebih tinggi';

  @override
  String get level_up_description_title => 'IDR Di Tangan';

  @override
  String get level_up_description_title1 => 'Cukup kembangkan 10 Agen Berlian!';

  @override
  String get invite_gold_agent => 'Undang Agen Emas';

  @override
  String get invite_diamond_agent => 'Undang Agen Berlian';

  @override
  String get level_up_bonus => 'Progres dan Hadiah Naik Level';

  @override
  String get activity_rule => 'Aturan Aktivitas';

  @override
  String get bonus => 'Hadiah';

  @override
  String get direct_invite_detail3 => 'Undang agen, hadiah Rp 35.000 / orang';

  @override
  String get team_bonus => 'Bonus Tim';

  @override
  String get team_bonus_detail =>
      'Rekomendasi tidak langsung (tingkat dua): setiap orang terkumpul Rp 100.000;\nRekomendasi tidak langsung (tingkat tiga): setiap orang terkumpul Rp 50.000;\nJika Anda mencapai status “Mitra Emas” (berhasil merekomendasikan langsung 10 mitra), hadiah dapat ditarik, berlaku selama 60 hari sejak hadiah dihasilkan. Jika tidak mencapai dalam waktu tersebut, hadiah akan hangus secara otomatis.';

  @override
  String get partner_extra_bonus3 =>
      'Tiap kembangkan 10 Mitra Emas: Hadiah Rp 2.000.000;\nTiap kembangkan 10 Mitra Berlian: Hadiah Rp 100.000.000.';

  @override
  String get invite_code_empty_hint => 'Kode undangan tidak boleh kosong!';

  @override
  String get copy_success => 'Berhasil Disalin';

  @override
  String get network_is_not_available =>
      'Jaringan tidak tersedia, silakan periksa koneksi Anda';

  @override
  String get login_with_other_method => 'Masuk dengan metode lain';

  @override
  String get member_introduction_level_silver_agent => 'Silver';

  @override
  String get normal_member_user => 'Normal';

  @override
  String get agree_with_payment_term =>
      'Apakah Anda setuju dengan syarat pembayaran?';

  @override
  String get please_choose_payment_method => 'Silakan pilih metode pembayaran';

  @override
  String get qrcode => 'KODE QR';

  @override
  String get open_payment_link => 'Buka Tautan Pembayaran Langsung';

  @override
  String get pay_with_qrcode => 'Bayar dengan QR Code';

  @override
  String get pay_with_qrcode_usage =>
      'Scan kode QR untuk membuka tautan pembayaran dan melakukan pembayaran. Jika ingin membayar langsung di aplikasi ini, ketuk untuk membuka tautannya.';

  @override
  String get jump_link_failed => 'Gagal Membuka Tautan';

  @override
  String get login_expired_hint =>
      'Login telah kedaluwarsa, silakan login kembali!';

  @override
  String get network_error => 'Kesalahan jaringan';

  @override
  String get unknown_error => 'Kesalahan tidak diketahui';
}
