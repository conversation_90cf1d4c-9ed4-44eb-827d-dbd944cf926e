// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class SZh extends S {
  SZh([String locale = 'zh']) : super(locale);

  @override
  String get home_navigation_home => '首页';

  @override
  String get home_navigation_brand => '品牌';

  @override
  String get home_navigation_income => '收入';

  @override
  String get home_navigation_mine => '我的';

  @override
  String get login_mobile_title => '手机登录';

  @override
  String get login_welcome_back => '欢迎回来';

  @override
  String get login_mobile_subtitle => '请使用手机号登录';

  @override
  String get login_phone_label => '手机号';

  @override
  String get login_phone_hint => '请输入手机号码';

  @override
  String get login_code_label => '验证码';

  @override
  String get login_code_hint => '请输入验证码';

  @override
  String get login_get_code => '获取验证码';

  @override
  String get login_code_seconds => '秒';

  @override
  String get login_button => '登录';

  @override
  String get login_password_alternative => '使用账号密码登录';

  @override
  String get login_enter_phone => '请输入手机号';

  @override
  String get login_code_sent => '验证码已发送';

  @override
  String get login_send_failed => '验证码发送失败';

  @override
  String get login_enter_phone_code => '请输入手机号和验证码';

  @override
  String get login_failed => '登录失败';

  @override
  String get error_title => '糟糕！出了些问题。请稍后重试。';

  @override
  String get error_button_title => '重试';

  @override
  String get loading_more_error => '加载失败，请重试';

  @override
  String get loading_more_no_more => '已经到底了哦~';

  @override
  String get loading_more_empty => '还没有壁纸哦 ~';

  @override
  String get loading_more_write => '写评分';

  @override
  String get loading_more_retry => '重试';

  @override
  String get home_rebate_rate_title => '返现率';

  @override
  String get home_platform_all => '全部';

  @override
  String get home_platform_hot_sale => '热门';

  @override
  String get home_platform_high_rebate => '高返现';

  @override
  String get home_platform_tiktok => 'TikTok';

  @override
  String get home_platform_shopee => 'Shopee';

  @override
  String get home_logo_slogan => '买前搜搜，件件返利';

  @override
  String get home_search_placeholder => '复制商品链接，拿返利';

  @override
  String get home_search_button_title => '粘贴';

  @override
  String get home_search_instructions_copy => '复制';

  @override
  String get home_search_instructions_text => '商品链接打开GENCO，拿返利';

  @override
  String get home_cashback_instructions_title => '赚取现金返现流程';

  @override
  String get home_cashback_instructions_check_all => '查看全部';

  @override
  String get home_cashback_instructions_step1 => '复制商品\n链接';

  @override
  String get home_cashback_instructions_step2 => '打开GENCO\n查看返现';

  @override
  String get home_cashback_instructions_step3 => '前往Shopee\n/TikTok';

  @override
  String get home_cashback_instructions_step4 => '获取返现';

  @override
  String get home_cashback_button_title => '预计返现金额';

  @override
  String get detail_price_title => '价格';

  @override
  String get detail_sold => '已售';

  @override
  String get detail_sold_count => '件';

  @override
  String get detail_cashback_amount => '预计返现金额';

  @override
  String get detail_rebate_rate => '返现率';

  @override
  String get detail_cashback_flow_title => '赚取现金返现流程';

  @override
  String get detail_cashback_flow_check => '查看教程';

  @override
  String get detail_cashback_flow_step1 => '点击产品';

  @override
  String get detail_cashback_flow_step2 => '在TikTok上\n订购商品';

  @override
  String get detail_cashback_flow_step3 => '跟踪订单';

  @override
  String get detail_cashback_flow_step4 => '获取现金\n返还';

  @override
  String get detail_brand_product_amount_pre => '共';

  @override
  String get detail_brand_product_amount => '件商品';

  @override
  String get brand_filter_all => '全部';

  @override
  String get brand_filter_price => '价格';

  @override
  String get brand_filter_rebate_rate => '返现率';

  @override
  String get brand_filter_sales => '销量';

  @override
  String get brand_filter_latest => '新品';

  @override
  String get usage_guideline_title => '使用指南';

  @override
  String get usage_guideline_description =>
      '通过链接查看现金返还在Shopee和TikTok仅需3步即可获得更多现金返还';

  @override
  String get usage_guideline_step1 =>
      '在 <red>Shopee</red> 或 <red>TikTok</red> 上打开商品并复制商品链接';

  @override
  String get usage_guideline_step2 => '返回<red>GENCO</red>，粘贴商品链接，并查看现金返还';

  @override
  String get usage_guideline_step3 =>
      '从<red>GENCO</red> 跳转至 <red>Shopee</red> 或 <red>TikTok</red> 完成下单';

  @override
  String get brand_home_top_title => '<red>严选</red>品质品牌';

  @override
  String get brand_home_top_subtitle => '邂逅心仪之选的同时畅享超值购物体验';

  @override
  String get brand_tiktok_hot_sale_title => 'TikTok热卖品牌';

  @override
  String get brand_high_rebate_title => '高返现品牌';

  @override
  String get brand_highest_rebate_rate => '最高返现率';

  @override
  String get message_no_data => '暂无数据';

  @override
  String get income_pre_total_income => '预计总收入';

  @override
  String get income_today => '今日收入';

  @override
  String get income_amount_to_be_credited => '待入账金额';

  @override
  String get income_amount_to_be_credited_hint =>
      '待入账收入主要包括已成功下单但尚未确认收货的订单返现收入，该金额仅作为参考或预估用途。';

  @override
  String get income_amount_credited => '已入账金额';

  @override
  String get income_amount_credited_description => '已成功到账的金额';

  @override
  String get income_amount_available_for_withdrawal => '可提现金额';

  @override
  String get income_withdrawal_button => '提现';

  @override
  String get income_withdrawal_success => '提现成功';

  @override
  String get income_withdrawal_failed => '提现失败';

  @override
  String get income_withdrawal_amount => '提现金额';

  @override
  String get income_withdrawal_amount_hint => '请输入提现金额';

  @override
  String get income_transaction_history => '交易记录';

  @override
  String get income_transaction_history_empty => '暂无交易记录';

  @override
  String get income_transaction_detail => '交易明细';

  @override
  String get income_my_order => '我的订单';

  @override
  String get income_income_detail => '收入明细';

  @override
  String get income_pre_total_income_description =>
      '预计总收入是已收到和未收到收入的预估，仅供参考，最终金额以实际支付价格为准。';

  @override
  String get ok => '确认';

  @override
  String get finish => '完成';

  @override
  String get credited_rebase_income => '返现订单收入';

  @override
  String get all => '全部';

  @override
  String get income => '收入';

  @override
  String get expenditure => '支出';

  @override
  String get rebase_income => '返现收入';

  @override
  String get rebase_expenditure => '返现支出';

  @override
  String get withdrawal_account => '提现账户';

  @override
  String get please_select_withdrawal_account => '请选择提现账户';

  @override
  String get withdrawal_amount => '提现金额';

  @override
  String get withdrawal_amount_hint => '最大可提现金额';

  @override
  String get withdrawal_amount_min => '最低可提现金额';

  @override
  String get withdrawal_all => '全部提现';

  @override
  String get withdrawal_finish => '提现完成';

  @override
  String get withdrawal_success => '提现申请已提交完成';

  @override
  String get withdrawal_success_hint =>
      '提取资金将在 24 小时内到账(周六、周日或节假日除外)，请随时关注账户资金变化!';

  @override
  String get withdrawal_failed => '提现失败';

  @override
  String get withdrawal_fees => '手续费';

  @override
  String get withdrawal_fees_hint =>
      '手续费为提现金额的 1.5%，系统将按比例计算。最低提现金额为 Rp5.550。若提现金额低于Rp5.550，费用将按 Rp5.550的标准收取。';

  @override
  String get withdrawal_hint => '温馨提示';

  @override
  String get withdrawal_hint_description => '提取资金将在 24 小时内到账（周六、周日或节假日除外）';

  @override
  String get trade_type => '交易类型';

  @override
  String get trade_time => '交易时间';

  @override
  String get trade_serial_number => '交易流水号';

  @override
  String get trade_channel => '交易渠道';

  @override
  String get trade_order_number => '订单编号';

  @override
  String get income_order_rebate => '订单返利';

  @override
  String get income_campaign_reward => '活动奖励';

  @override
  String get income_expected_total_amount => '预计返现总金额';

  @override
  String get income_expected_total_amount_hint =>
      '这是您已付款订单的预计现金返还总额。如果发生退款或退货，此预估金额可能会发生变化。\n现金返还预估仅供参考，最终金额将根据您支付的最终价格确定。';

  @override
  String get income_actual_credited_amount => '返现到账总金额';

  @override
  String get income_actual_credited_amount_hint => '订单成功确认完成后，计算得出的\n现金返还总金额。';

  @override
  String get order_title => '我的订单';

  @override
  String get order_tab_all => '全部';

  @override
  String get order_tab_processing => '处理中';

  @override
  String get order_tab_completed => '已完成';

  @override
  String get order_tab_expired => '已过期';

  @override
  String get order_application_time => '申请时间：';

  @override
  String get order_status_processing => '正在处理';

  @override
  String get order_status_completed => '已完成';

  @override
  String get order_status_expired => '已过期';

  @override
  String get order_expected_cashback => '预计返现金额：';

  @override
  String get order_cashback_info => '返现说明';

  @override
  String get rebate_instruction_title => '返现说明';

  @override
  String get rebate_instruction_content =>
      '完成订单后，我们将在 1–3 天内跟踪您的订单状态。在您确认收到货物且订单完成后，现金将退还至您的账户余额。';

  @override
  String get rebate_step_1_title => '01';

  @override
  String get rebate_step_1_content => '通过我们支持的应用程序比如Shopee/TikTok进行产品订购。';

  @override
  String get rebate_step_2_title => '02';

  @override
  String get rebate_step_2_content => '下单1–3天后跟踪订单状态。';

  @override
  String get rebate_step_3_title => '03';

  @override
  String get rebate_step_3_content => '确认订单完成后，获得返现。';

  @override
  String get rebate_how_to_get_title => '以下是在 Shopee 和 TikTok Shop 购物时获得返现的方法：';

  @override
  String get rebate_how_to_order_title => '如何下单';

  @override
  String get rebate_how_to_order_content =>
      '从我们支持的第三方平台复制产品链接后，请务必通过我们的应用程序下单，这将把您重定向回第三方平台。如果您想订购 3 种不同的产品，每个产品都必须通过我们的应用程序打开，以便每个产品订单都能被记录。';

  @override
  String get rebate_genco_last_app_title => 'GENCO是您最后打开的应用程序';

  @override
  String get rebate_genco_last_app_content =>
      '购物前，请确保我们的应用程序是您最后点击的应用程序。如果您在打开我们的应用程序后访问其他网站或应用程序，将无法获得返现。';

  @override
  String get rebate_unsupported_order_title => '不支持返现的订单';

  @override
  String get rebate_unsupported_order_content =>
      '购物前，请确保我们的应用程序是您最后点击的应用程序。如果您在打开我们的应用程序后访问其他网站或应用程序，将无法获得返现。';

  @override
  String get rebase_info => '返现详情';

  @override
  String get rebase_price => '购买价格';

  @override
  String get rebase_rate => '返现率';

  @override
  String get rebase_cash => '获得返现';

  @override
  String get withdrawal_choose_method => '选择提现账户';

  @override
  String get withdrawal_add_card => '添加银行卡';

  @override
  String get withdrawal_add_e_card => '添加电子钱包';

  @override
  String get name => '姓名';

  @override
  String get name_placeholder => '输入姓名';

  @override
  String get bank_name => '银行名称';

  @override
  String get select_bank => '选择银行';

  @override
  String get bank_card_number => '银行卡号';

  @override
  String get input_bank_card_number => '输入银行卡号';

  @override
  String get search => '搜索';

  @override
  String get button_next => '继续';

  @override
  String get bind_bank_card_confirm => '绑卡确认';

  @override
  String get e_wallet => '电子钱包';

  @override
  String get phone_number => '手机号';

  @override
  String get phone_number_placeholder => '输入手机号';

  @override
  String get select_e_wallet => '选择电子钱包';

  @override
  String get usage_hint => '与GENCO一起省钱';

  @override
  String get my_collection => '我的收藏';

  @override
  String get guide_step1_title => '四步获取现金返还';

  @override
  String get guide_step1_content_flow_1 => '在 Shopee 或 TikTok 上打开商品并复制商品链接';

  @override
  String get guide_step1_content_flow_2 => '在 GENCO 查看现金返还';

  @override
  String get guide_step1_content_flow_3 => '前往 Shopee/TikTok完成下单';

  @override
  String get guide_step1_content_flow_4 => '确认订单完成，获取现金返还';

  @override
  String get guide_step1_content_flow_1_title => '复制产品链接';

  @override
  String get guide_step1_content_flow_1_description =>
      '在 Shopee 或 TikTok 中选择产品，点击 “分享” ，选择复制产品链接。';

  @override
  String get guide_step1_content_flow_2_title => '在GENCO查看现金返还';

  @override
  String get guide_step1_content_flow_2_description =>
      '打开GENCO，现金返还金额将自动显示，或者将链接粘贴到搜索栏中。';

  @override
  String get guide_step1_content_flow_3_title =>
      '点击 “在 Shopee/TikTok 下单” 并进行订购';

  @override
  String get guide_step1_content_flow_3_description =>
      '在商品详情中点击 “在 Shopee/TikTok 下单”，跳转到 Shopee/TikTok 后，点击 “立即购买”。';

  @override
  String get guide_step1_content_flow_4_title => '追踪并领取你的现金返还';

  @override
  String get guide_step1_content_flow_4_description =>
      '系统会在 1 - 3 天内追踪你的订单。在 Shopee/TikTok 上点击 “订单已收货” 后，现金返还会直接到账！';

  @override
  String get guide_step1_content_flow_5_title => '如何领取现金返还？';

  @override
  String get guide_step1_content_flow_5_description => '观看教程视频,点击播放并学习详细步骤！';

  @override
  String get feedback_cash => '返现现金';

  @override
  String get edit => '编辑';

  @override
  String get cancel => '取消';

  @override
  String get select_all => '全选';

  @override
  String get cancel_select_all => '取消全选';

  @override
  String get delete => '删除';

  @override
  String get setting => '设置';

  @override
  String get my_avatar => '我的头像';

  @override
  String get nickname => '昵称';

  @override
  String get whatsapp_account => 'Whatsapp号';

  @override
  String get modify_phone_number => '修改手机号';

  @override
  String get modify_password => '修改登录密码';

  @override
  String get privacy => '隐私政策';

  @override
  String get about => '关于';

  @override
  String get modify_nickname => '修改昵称';

  @override
  String get login_with_tiktok => 'TikTok登录';

  @override
  String get login_title => '欢迎来到GENCO';

  @override
  String get login_subtitle => '购物省钱，分享赚钱';

  @override
  String get whatsapp_account_hint => '请输入您的Whatsapp号';

  @override
  String get next_step => '下一步';

  @override
  String get account_empty_hint => '账号不能为空';

  @override
  String get input_opt_verification_code => '请输入OTP验证码';

  @override
  String input_opt_verification_code_hint(String phone) {
    return '验证码已发送至您 $phone 的WhatsApp号';
  }

  @override
  String get login_with_password => '密码登录';

  @override
  String get resend_in => '重新获取';

  @override
  String get seconds => 's';

  @override
  String get resend_code => '重新发送';

  @override
  String get login => '登录';

  @override
  String get input_opt_verification_code_error => '请输入收到的验证码';

  @override
  String get login_success => '登录成功';

  @override
  String get welcome_back => 'Hi，欢迎回来';

  @override
  String get please_input_your_password => '请输入您的登录密码';

  @override
  String get login_password => '登录密码';

  @override
  String get input_password_hint => '输入登录密码';

  @override
  String get login_with_verification_code => '验证码登录';

  @override
  String get login_agreement => '登录账户即表示您已阅读并同意';

  @override
  String get and => '和';

  @override
  String get user_agreement => '用户协议';

  @override
  String get privacy_policy => '隐私政策';

  @override
  String get setting_login_password => '设置登录密码';

  @override
  String get set_password_hint => '请输入6-20位字母和数字组合的登录密码';

  @override
  String get login_password_confirm => '确认登录密码';

  @override
  String get login_password_confirm_hint => '再次输入登录密码';

  @override
  String get password_not_same => '两次输入的密码不一致';

  @override
  String get modify_success => '修改成功';

  @override
  String get find_product => '找到商品';

  @override
  String get check_cash_back => '查看现金返还';

  @override
  String get back => '返回';

  @override
  String get can_not_open_link => '无法点开链接';

  @override
  String get collection => '收藏';

  @override
  String get order_right_now => '立即下单';

  @override
  String get add_to_collection_success => '添加到收藏成功';

  @override
  String get please_select_bank_or_e_wallet => '请选择银行或者电子钱包';

  @override
  String get please_input_amount => '请输入提现金额';

  @override
  String get please_select_bank => '请选择银行';

  @override
  String get please_input_bank_number => '请输入银行卡号';

  @override
  String get please_select_e_wallet => '请输入电子钱包';

  @override
  String get please_input_e_wallet_account => '请输入电子钱包账号';

  @override
  String get logout => '退出登录';

  @override
  String get logout_confirm_title => '确认退出';

  @override
  String get logout_confirm_message => '确定要退出登录吗？';

  @override
  String get confirm => '确定';

  @override
  String get jump_to_tiktok => '即将跳转至 TikTok';

  @override
  String share_text(String amount) {
    return '立即通过GENCO下单，预计可获得返现Rp $amount';
  }

  @override
  String get nickname_hint => '请输入昵称';

  @override
  String get member_introduction => '升级成为代理或者合作伙伴赚取更多收益';

  @override
  String get member_level_state => 'Status Level';

  @override
  String get member_status_description => '您当前未开通代理或者合作伙伴';

  @override
  String get member_level_silver_agent => '银色代理';

  @override
  String get member_level_partner => '合作伙伴';

  @override
  String get member_level_silver_agent_fee => '银色代理费用';

  @override
  String get member_level_partner_fee => '合作伙伴费用';

  @override
  String get become_member => '成为代理';

  @override
  String get year => '年';

  @override
  String get delete_account => '注销账户';

  @override
  String get delete_account_title => '确定要注销账户?';

  @override
  String get delete_account_content => '账户注销后您所有的相关权益将全部丢失，\n并且无法再次登录。';

  @override
  String get product_link_empty => '没找链接对应的商品';

  @override
  String get product_link_empty_content => '该商品链接未找到对应的商品，请检查链接是否正确，或尝试其他商品。';

  @override
  String get exclusive_benefits => '专属权益';

  @override
  String get member_benefits_silver_agent_1 => '有效期';

  @override
  String get member_benefits_silver_agent_1_value => '1年';

  @override
  String get member_benefits_silver_agent_2 => '邀请奖金';

  @override
  String get member_benefits_silver_agent_2_value => '成功邀人，即可获额外奖励';

  @override
  String get member_benefits_silver_agent_3 => '团队购物佣金';

  @override
  String get member_benefits_silver_agent_3_value => '团队消费，你享佣金收益';

  @override
  String get member_benefits_silver_agent_4 => '额外现金返还';

  @override
  String get member_benefits_silver_agent_4_value => '现金返还更高，最高可达 50%';

  @override
  String get member_benefits_silver_agent_5 => '无限制现金返还';

  @override
  String get member_benefits_silver_agent_5_value => '现金返还无限制';

  @override
  String get member_benefits_partner_agent_1 => '有效期';

  @override
  String get member_benefits_partner_agent_1_value => '永久';

  @override
  String get member_benefits_partner_agent_2 => '邀请奖金';

  @override
  String get member_benefits_partner_agent_2_value => '成功后可获得高额奖金10亿+';

  @override
  String get member_benefits_partner_agent_3 => '团队购物佣金';

  @override
  String get member_benefits_partner_agent_3_value => '从下线的每笔现金返还中获得最高 20%返现';

  @override
  String get member_benefits_partner_agent_4 => '额外现金返还';

  @override
  String get member_benefits_partner_agent_4_value => '现金返还更高，最高可达 100%';

  @override
  String get role => '角色';

  @override
  String get benefit => '权益';

  @override
  String get normal_user => '普通用户';

  @override
  String get normal_user_2_benefit => '无奖金';

  @override
  String get agent => '代理';

  @override
  String get member_benefits_silver_agent_2_benefit => '可获得高达10.000.000印尼盾奖金';

  @override
  String get normal_user_3_benefit => '无购物佣金';

  @override
  String get member_benefits_silver_agent_3_benefit =>
      '直接推荐的购物现金返还的10%佣金（5%常规佣金+5%活动补贴） ';

  @override
  String get normal_user_4_benefit => '无购物佣金';

  @override
  String get member_benefits_silver_agent_4_benefit =>
      '直接推荐的购物现金返还的10%佣金（5%常规佣金+5%活动补贴） ';

  @override
  String get normal_user_5_benefit => '无购物佣金';

  @override
  String get member_benefits_silver_agent_5_benefit =>
      '直接推荐的购物现金返还的10%佣金（5%常规佣金+5%活动补贴） ';

  @override
  String get order_payment => '订单支付';

  @override
  String get order_price => '订单金额';

  @override
  String get product_name => '产品名称';

  @override
  String get real_payment_price => '实际支付金额';

  @override
  String get agent_fee => '代理费';

  @override
  String get purchase_right_now => '立即支付';

  @override
  String get payment_problem => '付款遇到问题';

  @override
  String get payment_complete => '已付款完成';

  @override
  String get payment_agreement => '阅读并同意';

  @override
  String get payment_agreement_link => '支付条款';

  @override
  String get cashback_is_0 => '该产品没有返现';

  @override
  String get cashback_is_0_content => '该产品没有返现是否继续？';

  @override
  String get nickname_too_long => '昵称太长了，请在十个字符以内';

  @override
  String get check_payment_result => '正在查看付款结果';

  @override
  String get payment_amount => '付款金额';

  @override
  String get payment_id => '付款流水号';

  @override
  String get payment_method => '付款方式';

  @override
  String get payment_success => '付款成功';

  @override
  String get payment_failed => '付款失败';

  @override
  String get level_status => '等级状态';

  @override
  String get valid_for => '有效期:1年';

  @override
  String get upgrade_date => '升级时间:2025年6月11日';

  @override
  String get to_gold_progress => '成为 金牌代理进度';

  @override
  String get invite_to_upgrade => '邀请10位朋友成为银牌代理或更高等级';

  @override
  String get silver_agent => '银牌代理';

  @override
  String get gold_agent => '金牌代理';

  @override
  String get diamond_agent => '钻石代理';

  @override
  String get partner => '合作伙伴';

  @override
  String get direct_invite_reward => '直接邀请奖励';

  @override
  String get direct_invite_detail => '每邀请 1 位代理，奖励 35,000 印尼盾，邀请3人直接回本！';

  @override
  String get team_purchase_bonus => '团队购物佣金';

  @override
  String get team_purchase_detail => '丰富收益，享受你直接邀请的团队购物金额的 10% 佣金';

  @override
  String get team_purchase_detail_gold =>
      '间接邀请（二级）：每人可累计 15.000 印尼盾；间接邀请（三级）：每人可累计 10.000 印尼盾。';

  @override
  String get training => '培训';

  @override
  String get training_detail => '专业导师提供高质量培训课程和综合辅导服务';

  @override
  String get extra_cashback => '额外现金返还';

  @override
  String get extra_cashback_detail_gold => '每发展 10 位黄金代理：奖励 300.000 印尼盾';

  @override
  String get extra_cashback_detail => '在特定时间内享受高现金返还的额外现金返还福利';

  @override
  String get invite_to_upgrade_empty => '邀请 10 位新朋友加入成为白银代理或更高等级。\\n你将自动升级状态！';

  @override
  String get silver_partner => '银牌合作伙伴';

  @override
  String get gold_partner => '金牌合作伙伴';

  @override
  String get diamond_partner => '钻石合作伙伴';

  @override
  String get partner_extra_bonus1 => '每发展 10 位银牌合作伙伴：奖励 1.000.000 印尼盾';

  @override
  String get partner_extra_bonus2 => '每发展 10 位黄金合作伙伴：奖励 2.000.000 印尼盾';

  @override
  String get direct_invite_detail2 => '每邀请 1 位代理商，奖励 200.000 印尼盾，邀请 3 人即可立即回本！';

  @override
  String get team_purchase_detail_gold2 =>
      '间接邀请（二级）：每人可获得 100.000 印尼盾；间接邀请（三级）：每人可获得 50.000 印尼盾';

  @override
  String get extra_bonus => '额外奖励';

  @override
  String get normal_member => '普通会员';

  @override
  String get high_cashback => '高额返现';

  @override
  String get high_cashback_description => '消费越多，省得越多';

  @override
  String get no_limit => '无限制';

  @override
  String get no_limit_description => '畅享无限制现金返现';

  @override
  String get user_service => '客户服务';

  @override
  String get user_service_description => '优质的客户服务';

  @override
  String get invite_and_eran_bonus => '邀请并赚取收益';

  @override
  String get invite_code => '邀请码';

  @override
  String get input_invite_code => '输入邀请码';

  @override
  String get contact_up => '联系负责人';

  @override
  String get congratulation_to_add_group => '恭喜你加入';

  @override
  String get group => '的团队';

  @override
  String get my_team => '我的团队';

  @override
  String get task_center => '任务中心';

  @override
  String get task_center_title => '任务中心';

  @override
  String get task_cash_income => '现金收益 (Rp)';

  @override
  String get task_withdraw => '去提现';

  @override
  String get task_withdrawable_amount => '可提现金额';

  @override
  String get task_daily_tasks => '任务列表';

  @override
  String get task_invite_reward => '邀新首单奖励';

  @override
  String get task_invite_progress => '邀请进度';

  @override
  String get task_order_progress => '完单进度';

  @override
  String get task_invite_count => '邀请人数';

  @override
  String get task_order_count => '完单数量';

  @override
  String get task_conditions_met => '条件已满足';

  @override
  String get task_conditions_not_met => '条件未满足';

  @override
  String get task_go_claim => '去领取';

  @override
  String get task_feature_developing => '功能开发中';

  @override
  String get task_developing => '请完成邀请和首单任务后再来领取奖励';

  @override
  String get task_return_cash_welfare => '回归现金福利';

  @override
  String get task_return_cash_welfare_desc => '每日专享';

  @override
  String get task_view_record => '查看记录';

  @override
  String get task_record_title => '任务记录';

  @override
  String get task_total_invites => '累计邀请人数';

  @override
  String get task_redeemed_invites => '已兑换邀请';

  @override
  String get task_total_orders => '累计完单数';

  @override
  String get task_redeemed_orders => '已兑换完单';

  @override
  String get task_close => '关闭';

  @override
  String get task_reward_amount => '奖励金额';

  @override
  String get task_per_completion => '每次完成';

  @override
  String get task_banner_title => '查看';

  @override
  String get task_banner_subtitle => '你的收入在这里！';

  @override
  String get task_banner_line1 => '越多交易和邀请';

  @override
  String get task_banner_line2 => '朋友，你的收入';

  @override
  String get task_banner_line3 => '也会越多。';

  @override
  String get invite_bonus => '邀请奖金';

  @override
  String get shopping_bonus => '购物奖金';

  @override
  String get cumulative_number_of_invitations => '累计邀请数量';

  @override
  String get today => '今日';

  @override
  String get invite_agent => '邀请代理';

  @override
  String get invite_normal_user => '邀请普通用户';

  @override
  String get silver => '银牌';

  @override
  String get gold => '金牌';

  @override
  String get diamond => '钻石';

  @override
  String get team_support => '团队贡献';

  @override
  String get received_bonus => '已收到奖金';

  @override
  String get invite_time => '邀请时间';

  @override
  String get pre_team_cashback => '预计团队贡献现金返还';

  @override
  String get invite_and_earn_money => '邀请并赚取收益';

  @override
  String get level_up_schedule => '升级进度';

  @override
  String get level_up_description => '升级说明';

  @override
  String get level_up_content_title => '邀请10位朋友成为银牌代理或更高等级';

  @override
  String get level_up_description_title => '印尼盾到手';

  @override
  String get level_up_description_title1 => '只需发展 10 个钻石代理！';

  @override
  String get invite_gold_agent => '邀请金牌代理';

  @override
  String get invite_diamond_agent => '邀请钻石代理';

  @override
  String get level_up_bonus => '升级进度与奖励';

  @override
  String get activity_rule => '活动规则';

  @override
  String get bonus => '奖励';

  @override
  String get direct_invite_detail3 => '邀请代理，奖励 35.000 印尼盾 / 人';

  @override
  String get team_bonus => '团队奖励';

  @override
  String get team_bonus_detail =>
      '非直接推荐（二级）：每人积累 100.000印尼盾；\n非直接推荐（三级）：每人积累 50.000印尼盾；\n若您达到 “金牌合作伙伴” 状态（成功直接推荐 10 名合作伙伴），奖金可提取，自奖金产生之日起 60 天内有效。若未在该期限内达成，奖金将自动失效。';

  @override
  String get partner_extra_bonus3 =>
      '每发展 10 名金牌合作伙伴：奖金 2.000.000印尼盾；\n每发展 10 名钻石合作伙伴：奖金 100.000.000印尼盾.';

  @override
  String get invite_code_empty_hint => '邀请码不能为空!';

  @override
  String get copy_success => '成功复制';

  @override
  String get network_is_not_available => '网络不可用，请检查网络连接';

  @override
  String get login_with_other_method => '使用其他方式登录';

  @override
  String get member_introduction_level_silver_agent => '银牌代理';

  @override
  String get normal_member_user => '普通用户';

  @override
  String get agree_with_payment_term => '您是否同意支付条款';

  @override
  String get please_choose_payment_method => '请选择支付方式';

  @override
  String get qrcode => '二维码';

  @override
  String get open_payment_link => '直接打开支付链接';

  @override
  String get pay_with_qrcode => '使用二维码支付';

  @override
  String get pay_with_qrcode_usage => '扫描二维码并打开支付链接支付，如果您想直接在本机支付请直接点击打开链接支付';

  @override
  String get jump_link_failed => '跳转失败';

  @override
  String get login_expired_hint => '登录已经过期，请重新登陆!';

  @override
  String get network_error => '网络错误';

  @override
  String get unknown_error => '未知错误';
}
