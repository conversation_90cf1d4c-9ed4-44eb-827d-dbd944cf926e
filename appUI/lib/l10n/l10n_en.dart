// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SEn extends S {
  SEn([String locale = 'en']) : super(locale);

  @override
  String get home_navigation_home => 'Home';

  @override
  String get home_navigation_brand => 'Brand';

  @override
  String get home_navigation_income => 'Income';

  @override
  String get home_navigation_mine => 'Mine';

  @override
  String get login_mobile_title => 'Phone Login';

  @override
  String get login_welcome_back => 'Welcome back';

  @override
  String get login_mobile_subtitle => 'Please login with your phone number';

  @override
  String get login_phone_label => 'Phone Number';

  @override
  String get login_phone_hint => 'Enter your phone number';

  @override
  String get login_code_label => 'Verification Code';

  @override
  String get login_code_hint => 'Enter the verification code';

  @override
  String get login_get_code => 'Get Code';

  @override
  String get login_code_seconds => 's';

  @override
  String get login_button => 'Login';

  @override
  String get login_password_alternative => 'Login with Password';

  @override
  String get login_enter_phone => 'Please enter phone number';

  @override
  String get login_code_sent => 'Verification code sent';

  @override
  String get login_send_failed => 'Failed to send code';

  @override
  String get login_enter_phone_code => 'Please enter phone number and code';

  @override
  String get login_failed => 'Login failed';

  @override
  String get error_title =>
      'Oops! Something went wrong. Please try again later.';

  @override
  String get error_button_title => 'Retry';

  @override
  String get loading_more_error => 'Loading failed, try again';

  @override
  String get loading_more_no_more => 'Oh it\'s over!';

  @override
  String get loading_more_empty => 'No wallpaper yet ~';

  @override
  String get loading_more_write => 'write a rating';

  @override
  String get loading_more_retry => 'Retry';

  @override
  String get home_rebate_rate_title => 'Cashback Rate';

  @override
  String get home_platform_all => 'All';

  @override
  String get home_platform_hot_sale => 'Hottest';

  @override
  String get home_platform_high_rebate => 'High Rebate';

  @override
  String get home_platform_tiktok => 'TikTok';

  @override
  String get home_platform_shopee => 'Shopee';

  @override
  String get home_logo_slogan => 'Check & Earn Rebates';

  @override
  String get home_search_placeholder => 'Copy product link, get cashback';

  @override
  String get home_search_button_title => 'Paste';

  @override
  String get home_search_instructions_copy => 'Copy';

  @override
  String get home_search_instructions_text =>
      'Open link in GENCO to get cashback';

  @override
  String get home_cashback_instructions_title => 'Earn Cashback Process';

  @override
  String get home_cashback_instructions_check_all => 'Check All';

  @override
  String get home_cashback_instructions_step1 => 'Copy product\nlink';

  @override
  String get home_cashback_instructions_step2 => 'Open GENCO\ncheck cashback';

  @override
  String get home_cashback_instructions_step3 => 'Go Shopee/\nTikTok';

  @override
  String get home_cashback_instructions_step4 => 'Get cashback';

  @override
  String get home_cashback_button_title => 'Est. Cashback';

  @override
  String get detail_price_title => 'Price';

  @override
  String get detail_sold => 'Sold out';

  @override
  String get detail_sold_count => '';

  @override
  String get detail_cashback_amount => 'Estimated Rebate Amount';

  @override
  String get detail_rebate_rate => 'Rebate Rate';

  @override
  String get detail_cashback_flow_title => 'Cashback Earning Process';

  @override
  String get detail_cashback_flow_check => 'View Tutorial';

  @override
  String get detail_cashback_flow_step1 => 'Click Product';

  @override
  String get detail_cashback_flow_step2 => 'Order on\nTikTok';

  @override
  String get detail_cashback_flow_step3 => 'Track Order';

  @override
  String get detail_cashback_flow_step4 => 'Get Cashback';

  @override
  String get detail_brand_product_amount_pre => 'Total';

  @override
  String get detail_brand_product_amount => 'products';

  @override
  String get brand_filter_all => 'All';

  @override
  String get brand_filter_price => 'Price';

  @override
  String get brand_filter_rebate_rate => 'Cashback Rate';

  @override
  String get brand_filter_sales => 'Sales';

  @override
  String get brand_filter_latest => 'New';

  @override
  String get usage_guideline_title => 'User Guide';

  @override
  String get usage_guideline_description =>
      'Get cashback on Shopee & TikTok in 3 steps via link';

  @override
  String get usage_guideline_step1 =>
      'Open product on Shopee/TikTok and copy link';

  @override
  String get usage_guideline_step2 => 'Paste link in GENCO to check cashback';

  @override
  String get usage_guideline_step3 => 'Jump to Shopee/TikTok to complete order';

  @override
  String get brand_home_top_title => '<red>Carefully Selected</red> Brands';

  @override
  String get brand_home_top_subtitle =>
      'Discover favorites while enjoying great shopping';

  @override
  String get brand_tiktok_hot_sale_title => 'TikTok Hot Brands';

  @override
  String get brand_high_rebate_title => 'High Cashback Brands';

  @override
  String get brand_highest_rebate_rate => 'Highest Cashback Rate';

  @override
  String get message_no_data => 'No Data';

  @override
  String get income_pre_total_income => 'Estimated Total Income';

  @override
  String get income_today => 'Today\'s Income';

  @override
  String get income_amount_to_be_credited => 'Pending Credit';

  @override
  String get income_amount_to_be_credited_hint =>
      'Includes unconfirmed order cashback. For reference only.';

  @override
  String get income_amount_credited => 'Credited Amount';

  @override
  String get income_amount_credited_description =>
      'Successfully credited amount';

  @override
  String get income_amount_available_for_withdrawal => 'Withdrawable Amount';

  @override
  String get income_withdrawal_button => 'Withdraw';

  @override
  String get income_withdrawal_success => 'Withdrawal Success';

  @override
  String get income_withdrawal_failed => 'Withdrawal Failed';

  @override
  String get income_withdrawal_amount => 'Withdrawal Amount';

  @override
  String get income_withdrawal_amount_hint => 'Enter amount';

  @override
  String get income_transaction_history => 'Transaction History';

  @override
  String get income_transaction_history_empty => 'No transactions yet';

  @override
  String get income_transaction_detail => 'Transaction Details';

  @override
  String get income_my_order => 'My Orders';

  @override
  String get income_income_detail => 'Income Details';

  @override
  String get income_pre_total_income_description =>
      'Estimated total income for reference. Final amount subject to actual payment.';

  @override
  String get ok => 'OK';

  @override
  String get finish => 'Complete';

  @override
  String get credited_rebase_income => 'Cashback Income';

  @override
  String get all => 'All';

  @override
  String get income => 'Income';

  @override
  String get expenditure => 'Expense';

  @override
  String get rebase_income => 'Cashback Income';

  @override
  String get rebase_expenditure => 'Cashback Expense';

  @override
  String get withdrawal_account => 'Withdrawal Account';

  @override
  String get please_select_withdrawal_account => 'Select account';

  @override
  String get withdrawal_amount => 'Withdrawal Amount';

  @override
  String get withdrawal_amount_hint => 'Max withdrawable';

  @override
  String get withdrawal_amount_min => 'Min withdrawable';

  @override
  String get withdrawal_all => 'Withdraw All';

  @override
  String get withdrawal_finish => 'Withdrawal Complete';

  @override
  String get withdrawal_success => 'Withdrawal submitted';

  @override
  String get withdrawal_success_hint =>
      'Funds arrive within 24h (excl. weekends/holidays)';

  @override
  String get withdrawal_failed => 'Withdrawal Failed';

  @override
  String get withdrawal_fees => 'Fee';

  @override
  String get withdrawal_fees_hint =>
      '1.5% fee. Min fee Rp5.550. If <Rp5.550, fee charged at Rp5.550.';

  @override
  String get withdrawal_hint => 'Note';

  @override
  String get withdrawal_hint_description =>
      'Funds arrive within 24h (excl. weekends/holidays)';

  @override
  String get trade_type => 'Transaction Type';

  @override
  String get trade_time => 'Time';

  @override
  String get trade_serial_number => 'Transaction ID';

  @override
  String get trade_channel => 'Channel';

  @override
  String get trade_order_number => 'Order ID';

  @override
  String get income_order_rebate => 'Order Cashback';

  @override
  String get income_campaign_reward => 'Campaign Reward';

  @override
  String get income_expected_total_amount => 'Estimated Cashback';

  @override
  String get income_expected_total_amount_hint =>
      'Estimated, subject to final credit';

  @override
  String get income_actual_credited_amount => 'Actual Cashback';

  @override
  String get income_actual_credited_amount_hint => 'Credited to account';

  @override
  String get order_title => 'My Orders';

  @override
  String get order_tab_all => 'All';

  @override
  String get order_tab_processing => 'Processing';

  @override
  String get order_tab_completed => 'Completed';

  @override
  String get order_tab_expired => 'Expired';

  @override
  String get order_application_time => 'Application Time:';

  @override
  String get order_status_processing => 'Processing';

  @override
  String get order_status_completed => 'Completed';

  @override
  String get order_status_expired => 'Expired';

  @override
  String get order_expected_cashback => 'Est. Cashback:';

  @override
  String get order_cashback_info => 'Cashback Info';

  @override
  String get rebate_instruction_title => 'Cashback Guide';

  @override
  String get rebate_instruction_content =>
      'Cashback credited after order confirmation & completion.';

  @override
  String get rebate_step_1_title => '01';

  @override
  String get rebate_step_1_content => 'Order via Shopee/TikTok';

  @override
  String get rebate_step_2_title => '02';

  @override
  String get rebate_step_2_content => 'Track status in 1-3 days';

  @override
  String get rebate_step_3_title => '03';

  @override
  String get rebate_step_3_content => 'Get cashback after completion';

  @override
  String get rebate_how_to_get_title => 'How to Earn Cashback:';

  @override
  String get rebate_how_to_order_title => 'How to Order';

  @override
  String get rebate_how_to_order_content =>
      'Place order via GENCO redirect to track cashback.';

  @override
  String get rebate_genco_last_app_title => 'Keep GENCO as Last App';

  @override
  String get rebate_genco_last_app_content =>
      'Cashback untraceable if other apps opened after GENCO.';

  @override
  String get rebate_unsupported_order_title => 'Ineligible Orders';

  @override
  String get rebate_unsupported_order_content =>
      'Orders not placed via GENCO redirect are ineligible.';

  @override
  String get rebase_info => 'Cashback Details';

  @override
  String get rebase_price => 'Purchase Price';

  @override
  String get rebase_rate => 'Cashback Rate';

  @override
  String get rebase_cash => 'Cashback Earned';

  @override
  String get withdrawal_choose_method => 'Select Account';

  @override
  String get withdrawal_add_card => 'Add Bank Card';

  @override
  String get withdrawal_add_e_card => 'Add e-Wallet';

  @override
  String get name => 'Name';

  @override
  String get name_placeholder => 'Enter name';

  @override
  String get bank_name => 'Bank Name';

  @override
  String get select_bank => 'Select Bank';

  @override
  String get bank_card_number => 'Card Number';

  @override
  String get input_bank_card_number => 'Enter card number';

  @override
  String get search => 'Search';

  @override
  String get button_next => 'Continue';

  @override
  String get bind_bank_card_confirm => 'Confirm Card';

  @override
  String get e_wallet => 'E-Wallet';

  @override
  String get phone_number => 'Phone';

  @override
  String get phone_number_placeholder => 'Enter Phone Number';

  @override
  String get select_e_wallet => 'Select e-Wallet';

  @override
  String get usage_hint => 'Save with GENCO';

  @override
  String get my_collection => 'My Collections';

  @override
  String get guide_step1_title => '4 Steps to Cashback';

  @override
  String get guide_step1_content_flow_1 => 'Copy product link on Shopee/TikTok';

  @override
  String get guide_step1_content_flow_2 => 'Check cashback in GENCO';

  @override
  String get guide_step1_content_flow_3 => 'Order on Shopee/TikTok';

  @override
  String get guide_step1_content_flow_4 => 'Confirm order & get cashback';

  @override
  String get guide_step1_content_flow_1_title => 'Copy Product Link';

  @override
  String get guide_step1_content_flow_1_description =>
      'Tap \'Share\' > \'Copy Link\' on Shopee/TikTok';

  @override
  String get guide_step1_content_flow_2_title => 'Check Cashback in GENCO';

  @override
  String get guide_step1_content_flow_2_description =>
      'Paste link in GENCO to see cashback';

  @override
  String get guide_step1_content_flow_3_title =>
      'Tap \'Order on Shopee/TikTok\'';

  @override
  String get guide_step1_content_flow_3_description =>
      'Jump to Shopee/TikTok to buy';

  @override
  String get guide_step1_content_flow_4_title => 'Track & Get Cashback';

  @override
  String get guide_step1_content_flow_4_description =>
      'Cashback credited after order confirmation';

  @override
  String get guide_step1_content_flow_5_title => 'How to Get Cashback?';

  @override
  String get guide_step1_content_flow_5_description =>
      'Watch tutorial video for details';

  @override
  String get feedback_cash => 'Cashback';

  @override
  String get edit => 'Edit';

  @override
  String get cancel => 'Cancel';

  @override
  String get select_all => 'Select All';

  @override
  String get cancel_select_all => 'Deselect All';

  @override
  String get delete => 'Delete';

  @override
  String get setting => 'Settings';

  @override
  String get my_avatar => 'My Avatar';

  @override
  String get nickname => 'Nickname';

  @override
  String get whatsapp_account => 'WhatsApp';

  @override
  String get modify_phone_number => 'Change Phone';

  @override
  String get modify_password => 'Change Password';

  @override
  String get privacy => 'Privacy';

  @override
  String get about => 'About';

  @override
  String get modify_nickname => 'Change Nickname';

  @override
  String get login_with_tiktok => 'Login with TikTok';

  @override
  String get login_title => 'Welcome to GENCO';

  @override
  String get login_subtitle => 'Save on shopping,\nEarn by sharing';

  @override
  String get whatsapp_account_hint => 'Enter WhatsApp number';

  @override
  String get next_step => 'Next';

  @override
  String get account_empty_hint => 'Account required';

  @override
  String get input_opt_verification_code => 'Enter OTP';

  @override
  String input_opt_verification_code_hint(String phone) {
    return 'Sent to WhatsApp $phone';
  }

  @override
  String get login_with_password => 'Password Login';

  @override
  String get resend_in => 'Resend in';

  @override
  String get seconds => 's';

  @override
  String get resend_code => 'Resend Code';

  @override
  String get login => 'Login';

  @override
  String get input_opt_verification_code_error => 'Enter code';

  @override
  String get login_success => 'Login Success';

  @override
  String get welcome_back => 'Welcome back!';

  @override
  String get please_input_your_password => 'Enter password';

  @override
  String get login_password => 'Password';

  @override
  String get input_password_hint => 'Enter password';

  @override
  String get login_with_verification_code => 'Code Login';

  @override
  String get login_agreement => 'By logging in, you agree to the';

  @override
  String get and => 'and';

  @override
  String get user_agreement => 'Terms of Service';

  @override
  String get privacy_policy => 'Privacy Policy';

  @override
  String get setting_login_password => 'Set Password';

  @override
  String get set_password_hint => '6-20 letters + numbers';

  @override
  String get login_password_confirm => 'Confirm Password';

  @override
  String get login_password_confirm_hint => 'Re-enter password';

  @override
  String get password_not_same => 'Passwords don\'t match';

  @override
  String get modify_success => 'Updated';

  @override
  String get find_product => 'Found Product';

  @override
  String get check_cash_back => 'View Cashback';

  @override
  String get back => 'Back';

  @override
  String get can_not_open_link => 'Can\'t open link';

  @override
  String get collection => 'Collection';

  @override
  String get order_right_now => 'Order Now';

  @override
  String get add_to_collection_success => 'Added to Collections';

  @override
  String get please_select_bank_or_e_wallet => 'Select bank/e-wallet';

  @override
  String get please_input_amount => 'Enter amount';

  @override
  String get please_select_bank => 'Select bank';

  @override
  String get please_input_bank_number => 'Enter card number';

  @override
  String get please_select_e_wallet => 'Select e-wallet';

  @override
  String get please_input_e_wallet_account => 'Enter e-wallet';

  @override
  String get logout => 'Logout';

  @override
  String get logout_confirm_title => 'Confirm Logout';

  @override
  String get logout_confirm_message => 'Log out now?';

  @override
  String get confirm => 'Confirm';

  @override
  String get jump_to_tiktok => 'Will jump to TikTok';

  @override
  String share_text(String amount) {
    return 'Place your order immediately through GENCO to receive an estimated cashback of Rp $amount';
  }

  @override
  String get nickname_hint => 'Please input nickname';

  @override
  String get member_introduction =>
      'Upgrade to become an agent or partner to earn more';

  @override
  String get member_level_state => 'Level Status';

  @override
  String get member_status_description =>
      'You are not currently an agent or partner';

  @override
  String get member_level_silver_agent => 'Silver Agent';

  @override
  String get member_level_partner => 'Partner';

  @override
  String get member_level_silver_agent_fee => 'Silver Agent Fee';

  @override
  String get member_level_partner_fee => 'Partner Fee';

  @override
  String get become_member => 'Become Agent';

  @override
  String get year => 'Year';

  @override
  String get delete_account => 'Deactivate Account';

  @override
  String get delete_account_title => 'Confirm Account Deletion?';

  @override
  String get delete_account_content =>
      'After account deletion, all your privileges and benefits will be permanently lost,\nand you will permanently lose access to your account.';

  @override
  String get product_link_empty => 'No product found for the link';

  @override
  String get product_link_empty_content =>
      'No matching product found for this link. Please check the link or try another product.';

  @override
  String get exclusive_benefits => 'Exclusive Benefits';

  @override
  String get member_benefits_silver_agent_1 => 'Validity Period';

  @override
  String get member_benefits_silver_agent_1_value => '1 Year';

  @override
  String get member_benefits_silver_agent_2 => 'Referral Bonus';

  @override
  String get member_benefits_silver_agent_2_value =>
      'Get extra rewards for successful referrals';

  @override
  String get member_benefits_silver_agent_3 => 'Team Shopping Commission';

  @override
  String get member_benefits_silver_agent_3_value =>
      'Earn commissions from your team\'s purchases';

  @override
  String get member_benefits_silver_agent_4 => 'Extra Cashback';

  @override
  String get member_benefits_silver_agent_4_value =>
      'Higher cashback up to 50%';

  @override
  String get member_benefits_silver_agent_5 => 'Unlimited Cashback';

  @override
  String get member_benefits_silver_agent_5_value => 'Cashback without limits';

  @override
  String get member_benefits_partner_agent_1 => 'Validity Period';

  @override
  String get member_benefits_partner_agent_1_value => 'Permanent';

  @override
  String get member_benefits_partner_agent_2 => 'Referral Bonus';

  @override
  String get member_benefits_partner_agent_2_value =>
      'Receive high bonuses up to 1 billion+ upon success';

  @override
  String get member_benefits_partner_agent_3 => 'Team Shopping Commission';

  @override
  String get member_benefits_partner_agent_3_value =>
      'Earn up to 20% cashback from each downline\'s cashback';

  @override
  String get member_benefits_partner_agent_4 => 'Extra Cashback';

  @override
  String get member_benefits_partner_agent_4_value =>
      'Higher cashback up to 100%';

  @override
  String get role => 'Role';

  @override
  String get benefit => 'Benefit';

  @override
  String get normal_user => 'Regular User';

  @override
  String get normal_user_2_benefit => 'No bonus';

  @override
  String get agent => 'Agent';

  @override
  String get member_benefits_silver_agent_2_benefit =>
      'Can earn up to 10,000,000 IDR bonus';

  @override
  String get normal_user_3_benefit => 'No shopping commission';

  @override
  String get member_benefits_silver_agent_3_benefit =>
      '10% commission from direct referrals\' shopping cashback (5% regular + 5% promotion)';

  @override
  String get normal_user_4_benefit => 'No shopping commission';

  @override
  String get member_benefits_silver_agent_4_benefit =>
      '10% commission from direct referrals\' shopping cashback (5% regular + 5% promotion)';

  @override
  String get normal_user_5_benefit => 'No shopping commission';

  @override
  String get member_benefits_silver_agent_5_benefit =>
      '10% commission from direct referrals\' shopping cashback (5% regular + 5% promotion)';

  @override
  String get order_payment => 'Order Payment';

  @override
  String get order_price => 'Order Amount';

  @override
  String get product_name => 'Product Name';

  @override
  String get real_payment_price => 'Actual Payment Amount';

  @override
  String get agent_fee => 'Agent Fee';

  @override
  String get purchase_right_now => 'Pay Now';

  @override
  String get payment_problem => 'Payment Issue';

  @override
  String get payment_complete => 'Completed';

  @override
  String get payment_agreement => 'Read and Agree';

  @override
  String get payment_agreement_link => 'Payment Terms';

  @override
  String get cashback_is_0 => 'This product has no cashback';

  @override
  String get cashback_is_0_content =>
      'There is no cashback for this product. Do you want to continue?';

  @override
  String get nickname_too_long => 'Nickname is too long, maximum 10 characters';

  @override
  String get check_payment_result => 'Checking Payment Result';

  @override
  String get payment_amount => 'Payment Amount';

  @override
  String get payment_id => 'Payment Transaction ID';

  @override
  String get payment_method => 'Payment Method';

  @override
  String get payment_success => 'Payment Successful';

  @override
  String get payment_failed => 'Payment Failed';

  @override
  String get level_status => 'Level Status';

  @override
  String get valid_for => 'Valid for: 1 year';

  @override
  String get upgrade_date => 'Upgrade Date: June 11, 2025';

  @override
  String get to_gold_progress => 'Progress to Gold Agent';

  @override
  String get invite_to_upgrade =>
      'Invite 10 friends to become Silver Agents or higher';

  @override
  String get silver_agent => 'Silver Agent';

  @override
  String get gold_agent => 'Gold Agent';

  @override
  String get diamond_agent => 'Diamond Agent';

  @override
  String get partner => 'Partner';

  @override
  String get direct_invite_reward => 'Direct Invitation Reward';

  @override
  String get direct_invite_detail =>
      'Invite 1 agent to get 35,000 IDR reward. Invite 3 to break even!';

  @override
  String get team_purchase_bonus => 'Team Purchase Commission';

  @override
  String get team_purchase_detail =>
      'Earn 10% commission on shopping of your directly invited team';

  @override
  String get team_purchase_detail_gold =>
      'Indirect invitation (level 2): Each accumulates 15,000 IDR; Indirect invitation (level 3): Each accumulates 10,000 IDR.';

  @override
  String get training => 'Training';

  @override
  String get training_detail =>
      'Professional mentors provide high-quality courses and guidance';

  @override
  String get extra_cashback => 'Extra Cashback';

  @override
  String get extra_cashback_detail_gold =>
      'For every 10 Gold Agents developed: 300,000 IDR reward';

  @override
  String get extra_cashback_detail =>
      'Enjoy extra cashback benefits during specified periods';

  @override
  String get invite_to_upgrade_empty =>
      'Invite 10 new friends to become Silver Agents or higher.\\nYour status will upgrade automatically!';

  @override
  String get silver_partner => 'Silver Partner';

  @override
  String get gold_partner => 'Gold Partner';

  @override
  String get diamond_partner => 'Diamond Partner';

  @override
  String get partner_extra_bonus1 =>
      'For every 10 Silver Partners developed: 1,000,000 IDR reward';

  @override
  String get partner_extra_bonus2 =>
      'For every 10 Gold Partners developed: 2,000,000 IDR reward';

  @override
  String get direct_invite_detail2 =>
      'Reward 200,000 IDR per agent invited, break even by inviting just 3!';

  @override
  String get team_purchase_detail_gold2 =>
      'Indirect invitation (level 2): Each gets 100,000 IDR; Indirect invitation (level 3): Each gets 50,000 IDR';

  @override
  String get extra_bonus => 'Extra Bonus';

  @override
  String get normal_member => 'Regular Member';

  @override
  String get high_cashback => 'High Cashback';

  @override
  String get high_cashback_description => 'Spend more, save more';

  @override
  String get no_limit => 'Unlimited';

  @override
  String get no_limit_description => 'Enjoy unlimited cashback';

  @override
  String get user_service => 'Customer Service';

  @override
  String get user_service_description => 'Premium customer service';

  @override
  String get invite_and_eran_bonus => 'Invite and Earn';

  @override
  String get invite_code => 'Invitation Code';

  @override
  String get input_invite_code => 'Enter Invitation Code';

  @override
  String get contact_up => 'Contact Supervisor';

  @override
  String get congratulation_to_add_group => 'Congratulations on joining';

  @override
  String get group => '\'s team';

  @override
  String get my_team => 'My Team';

  @override
  String get task_center => 'Task Center';

  @override
  String get task_center_title => 'Task Center';

  @override
  String get task_cash_income => 'Cash Income (Rp)';

  @override
  String get task_withdraw => 'Withdraw';

  @override
  String get task_withdrawable_amount => 'Withdrawable Amount';

  @override
  String get task_daily_tasks => 'Task List';

  @override
  String get task_invite_reward => 'Invite First Order Reward';

  @override
  String get task_invite_progress => 'Invite Progress';

  @override
  String get task_order_progress => 'Order Progress';

  @override
  String get task_invite_count => 'Invite Count';

  @override
  String get task_order_count => 'Order Count';

  @override
  String get task_conditions_met => 'Conditions Met';

  @override
  String get task_conditions_not_met => 'Conditions Not Met';

  @override
  String get task_go_claim => 'Claim';

  @override
  String get task_feature_developing => 'Feature Developing';

  @override
  String get task_developing =>
      'Please complete invitation and first order tasks before claiming rewards';

  @override
  String get task_return_cash_welfare => 'Return Cash Welfare';

  @override
  String get task_return_cash_welfare_desc => 'Daily Exclusive';

  @override
  String get task_view_record => 'View Record';

  @override
  String get task_record_title => 'Task Record';

  @override
  String get task_total_invites => 'Total Invites';

  @override
  String get task_redeemed_invites => 'Redeemed Invites';

  @override
  String get task_total_orders => 'Total Orders';

  @override
  String get task_redeemed_orders => 'Redeemed Orders';

  @override
  String get task_close => 'Close';

  @override
  String get task_reward_amount => 'Reward Amount';

  @override
  String get task_per_completion => 'Per Completion';

  @override
  String get task_banner_title => 'Check';

  @override
  String get task_banner_subtitle => 'Your Earnings Here!';

  @override
  String get task_banner_line1 => 'The more transactions and friend';

  @override
  String get task_banner_line2 => 'invitations, the more';

  @override
  String get task_banner_line3 => 'your earnings.';

  @override
  String get invite_bonus => 'Referral Bonus';

  @override
  String get shopping_bonus => 'Shopping Bonus';

  @override
  String get cumulative_number_of_invitations => 'Total Invitations';

  @override
  String get today => 'Today';

  @override
  String get invite_agent => 'Invite Agent';

  @override
  String get invite_normal_user => 'Invite Regular User';

  @override
  String get silver => 'Silver';

  @override
  String get gold => 'Gold';

  @override
  String get diamond => 'Diamond';

  @override
  String get team_support => 'Team Contribution';

  @override
  String get received_bonus => 'Received Bonus';

  @override
  String get invite_time => 'Invitation Time';

  @override
  String get pre_team_cashback => 'Estimated Team Contribution Cashback';

  @override
  String get invite_and_earn_money => 'Invite and Earn Money';

  @override
  String get level_up_schedule => 'Level Up Progress';

  @override
  String get level_up_description => 'Upgrade Instructions';

  @override
  String get level_up_content_title =>
      'Invite 10 friends to become Silver Agents or higher';

  @override
  String get level_up_description_title => 'IDR in Hand';

  @override
  String get level_up_description_title1 => 'Just develop 10 Diamond Agents!';

  @override
  String get invite_gold_agent => 'Invite Gold Agent';

  @override
  String get invite_diamond_agent => 'Invite Diamond Agent';

  @override
  String get level_up_bonus => 'Level Up Progress & Rewards';

  @override
  String get activity_rule => 'Activity Rules';

  @override
  String get bonus => 'Reward';

  @override
  String get direct_invite_detail3 =>
      'Invite agents: 35,000 IDR reward per person';

  @override
  String get team_bonus => 'Team Bonus';

  @override
  String get team_bonus_detail =>
      'Indirect referral (level 2): Each accumulates 100,000 IDR;\nIndirect referral (level 3): Each accumulates 50,000 IDR;\nIf you reach \"Gold Partner\" status (successfully refer 10 partners directly), bonus becomes withdrawable. Valid for 60 days from generation. If not achieved, bonus expires.';

  @override
  String get partner_extra_bonus3 =>
      'For every 10 Gold Partners developed: 2,000,000 IDR bonus;\nFor every 10 Diamond Partners developed: 100,000,000 IDR bonus.';

  @override
  String get invite_code_empty_hint => 'Invitation code cannot be empty!';

  @override
  String get copy_success => 'Copied Successfully';

  @override
  String get network_is_not_available =>
      'Network is unavailable, please check your connection';

  @override
  String get login_with_other_method => 'Login with other method';

  @override
  String get member_introduction_level_silver_agent => 'Silver';

  @override
  String get normal_member_user => 'Normal';

  @override
  String get agree_with_payment_term => 'Do you agree with the payment terms?';

  @override
  String get please_choose_payment_method => 'Please select a payment method';

  @override
  String get qrcode => 'QRCode';

  @override
  String get open_payment_link => 'Open Payment Link Directly';

  @override
  String get pay_with_qrcode => 'Pay with QR Code';

  @override
  String get pay_with_qrcode_usage =>
      'Scan the QR code to open the payment link and pay. If you want to pay directly in this app, tap to open the link.';

  @override
  String get jump_link_failed => 'Jump Link Failed';

  @override
  String get login_expired_hint => 'Login has expired, please log in again!';

  @override
  String get network_error => 'Network error';

  @override
  String get unknown_error => 'Unknown error';
}
