import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'l10n_en.dart';
import 'l10n_id.dart';
import 'l10n_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of S
/// returned by `S.of(context)`.
///
/// Applications need to include `S.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/l10n.dart';
///
/// return MaterialApp(
///   localizationsDelegates: S.localizationsDelegates,
///   supportedLocales: S.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the S.supportedLocales
/// property.
abstract class S {
  S(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static S? of(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  static const LocalizationsDelegate<S> delegate = _SDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('id'),
    Locale('zh'),
  ];

  /// No description provided for @home_navigation_home.
  ///
  /// In id, this message translates to:
  /// **'Beranda'**
  String get home_navigation_home;

  /// No description provided for @home_navigation_brand.
  ///
  /// In id, this message translates to:
  /// **'Brand'**
  String get home_navigation_brand;

  /// No description provided for @home_navigation_income.
  ///
  /// In id, this message translates to:
  /// **'Pendapatan'**
  String get home_navigation_income;

  /// No description provided for @home_navigation_mine.
  ///
  /// In id, this message translates to:
  /// **'Saya'**
  String get home_navigation_mine;

  /// No description provided for @login_mobile_title.
  ///
  /// In id, this message translates to:
  /// **'Masuk via Nomor Ponsel'**
  String get login_mobile_title;

  /// No description provided for @login_welcome_back.
  ///
  /// In id, this message translates to:
  /// **'Selamat Datang Kembali'**
  String get login_welcome_back;

  /// No description provided for @login_mobile_subtitle.
  ///
  /// In id, this message translates to:
  /// **'Silakan masuk dengan nomor ponsel'**
  String get login_mobile_subtitle;

  /// No description provided for @login_phone_label.
  ///
  /// In id, this message translates to:
  /// **'Nomor Ponsel'**
  String get login_phone_label;

  /// No description provided for @login_phone_hint.
  ///
  /// In id, this message translates to:
  /// **'Masukkan nomor ponsel'**
  String get login_phone_hint;

  /// No description provided for @login_code_label.
  ///
  /// In id, this message translates to:
  /// **'Kode Verifikasi'**
  String get login_code_label;

  /// No description provided for @login_code_hint.
  ///
  /// In id, this message translates to:
  /// **'Masukkan kode verifikasi'**
  String get login_code_hint;

  /// No description provided for @login_get_code.
  ///
  /// In id, this message translates to:
  /// **'Dapatkan Kode'**
  String get login_get_code;

  /// No description provided for @login_code_seconds.
  ///
  /// In id, this message translates to:
  /// **'detik'**
  String get login_code_seconds;

  /// No description provided for @login_button.
  ///
  /// In id, this message translates to:
  /// **'Masuk'**
  String get login_button;

  /// No description provided for @login_password_alternative.
  ///
  /// In id, this message translates to:
  /// **'Masuk dengan Sandi'**
  String get login_password_alternative;

  /// No description provided for @login_enter_phone.
  ///
  /// In id, this message translates to:
  /// **'Harap masukkan nomor ponsel'**
  String get login_enter_phone;

  /// No description provided for @login_code_sent.
  ///
  /// In id, this message translates to:
  /// **'Kode verifikasi telah dikirim'**
  String get login_code_sent;

  /// No description provided for @login_send_failed.
  ///
  /// In id, this message translates to:
  /// **'Pengiriman kode gagal'**
  String get login_send_failed;

  /// No description provided for @login_enter_phone_code.
  ///
  /// In id, this message translates to:
  /// **'Masukkan nomor ponsel dan kode'**
  String get login_enter_phone_code;

  /// No description provided for @login_failed.
  ///
  /// In id, this message translates to:
  /// **'Gagal masuk'**
  String get login_failed;

  /// No description provided for @error_title.
  ///
  /// In id, this message translates to:
  /// **'Maaf! Terjadi kesalahan. Silakan coba lagi.'**
  String get error_title;

  /// No description provided for @error_button_title.
  ///
  /// In id, this message translates to:
  /// **'Coba Lagi'**
  String get error_button_title;

  /// No description provided for @loading_more_error.
  ///
  /// In id, this message translates to:
  /// **'Gagal memuat, silakan coba lagi'**
  String get loading_more_error;

  /// No description provided for @loading_more_no_more.
  ///
  /// In id, this message translates to:
  /// **'Sudah sampai akhir~'**
  String get loading_more_no_more;

  /// No description provided for @loading_more_empty.
  ///
  /// In id, this message translates to:
  /// **'Belum ada wallpaper ~'**
  String get loading_more_empty;

  /// No description provided for @loading_more_write.
  ///
  /// In id, this message translates to:
  /// **'Tulis Ulasan'**
  String get loading_more_write;

  /// No description provided for @loading_more_retry.
  ///
  /// In id, this message translates to:
  /// **'Coba Lagi'**
  String get loading_more_retry;

  /// No description provided for @home_rebate_rate_title.
  ///
  /// In id, this message translates to:
  /// **'Persentase Cashback'**
  String get home_rebate_rate_title;

  /// No description provided for @home_platform_all.
  ///
  /// In id, this message translates to:
  /// **'Semua'**
  String get home_platform_all;

  /// No description provided for @home_platform_hot_sale.
  ///
  /// In id, this message translates to:
  /// **'Terlaris'**
  String get home_platform_hot_sale;

  /// No description provided for @home_platform_high_rebate.
  ///
  /// In id, this message translates to:
  /// **'Cashback Tinggi'**
  String get home_platform_high_rebate;

  /// No description provided for @home_platform_tiktok.
  ///
  /// In id, this message translates to:
  /// **'TikTok'**
  String get home_platform_tiktok;

  /// No description provided for @home_platform_shopee.
  ///
  /// In id, this message translates to:
  /// **'Shopee'**
  String get home_platform_shopee;

  /// No description provided for @home_logo_slogan.
  ///
  /// In id, this message translates to:
  /// **'Cari & Bandingkan, Dapat Cashback!'**
  String get home_logo_slogan;

  /// No description provided for @home_search_placeholder.
  ///
  /// In id, this message translates to:
  /// **'Salin tautan produk, dapatkan cashback'**
  String get home_search_placeholder;

  /// No description provided for @home_search_button_title.
  ///
  /// In id, this message translates to:
  /// **'Tempel'**
  String get home_search_button_title;

  /// No description provided for @home_search_instructions_copy.
  ///
  /// In id, this message translates to:
  /// **'Salin'**
  String get home_search_instructions_copy;

  /// No description provided for @home_search_instructions_text.
  ///
  /// In id, this message translates to:
  /// **'Buka tautan di GENCO, raih cashback'**
  String get home_search_instructions_text;

  /// No description provided for @home_cashback_instructions_title.
  ///
  /// In id, this message translates to:
  /// **'Cara Dapatkan Cashback'**
  String get home_cashback_instructions_title;

  /// No description provided for @home_cashback_instructions_check_all.
  ///
  /// In id, this message translates to:
  /// **'Lihat Semua'**
  String get home_cashback_instructions_check_all;

  /// No description provided for @home_cashback_instructions_step1.
  ///
  /// In id, this message translates to:
  /// **'Salin\ntautan produk'**
  String get home_cashback_instructions_step1;

  /// No description provided for @home_cashback_instructions_step2.
  ///
  /// In id, this message translates to:
  /// **'Buka GENCO\nCek cashback'**
  String get home_cashback_instructions_step2;

  /// No description provided for @home_cashback_instructions_step3.
  ///
  /// In id, this message translates to:
  /// **'Beli di\nShopee/TikTok'**
  String get home_cashback_instructions_step3;

  /// No description provided for @home_cashback_instructions_step4.
  ///
  /// In id, this message translates to:
  /// **'Dapatkan Cashback'**
  String get home_cashback_instructions_step4;

  /// No description provided for @home_cashback_button_title.
  ///
  /// In id, this message translates to:
  /// **'Perkiraan jumlah cashback'**
  String get home_cashback_button_title;

  /// No description provided for @detail_price_title.
  ///
  /// In id, this message translates to:
  /// **'Harga'**
  String get detail_price_title;

  /// No description provided for @detail_sold.
  ///
  /// In id, this message translates to:
  /// **'Terjual'**
  String get detail_sold;

  /// No description provided for @detail_sold_count.
  ///
  /// In id, this message translates to:
  /// **'pcs'**
  String get detail_sold_count;

  /// No description provided for @detail_cashback_amount.
  ///
  /// In id, this message translates to:
  /// **'Perkiraan Cashback'**
  String get detail_cashback_amount;

  /// No description provided for @detail_rebate_rate.
  ///
  /// In id, this message translates to:
  /// **'Persentase Cashback'**
  String get detail_rebate_rate;

  /// No description provided for @detail_cashback_flow_title.
  ///
  /// In id, this message translates to:
  /// **'Alur Dapatkan Cashback'**
  String get detail_cashback_flow_title;

  /// No description provided for @detail_cashback_flow_check.
  ///
  /// In id, this message translates to:
  /// **'Lihat Panduan'**
  String get detail_cashback_flow_check;

  /// No description provided for @detail_cashback_flow_step1.
  ///
  /// In id, this message translates to:
  /// **'Klik produk'**
  String get detail_cashback_flow_step1;

  /// No description provided for @detail_cashback_flow_step2.
  ///
  /// In id, this message translates to:
  /// **'Pesan via\nTikTok'**
  String get detail_cashback_flow_step2;

  /// No description provided for @detail_cashback_flow_step3.
  ///
  /// In id, this message translates to:
  /// **'Lacak Pesanan'**
  String get detail_cashback_flow_step3;

  /// No description provided for @detail_cashback_flow_step4.
  ///
  /// In id, this message translates to:
  /// **'Dapatkan\nCashback'**
  String get detail_cashback_flow_step4;

  /// No description provided for @detail_brand_product_amount_pre.
  ///
  /// In id, this message translates to:
  /// **'Total'**
  String get detail_brand_product_amount_pre;

  /// No description provided for @detail_brand_product_amount.
  ///
  /// In id, this message translates to:
  /// **'produk'**
  String get detail_brand_product_amount;

  /// No description provided for @brand_filter_all.
  ///
  /// In id, this message translates to:
  /// **'Semua'**
  String get brand_filter_all;

  /// No description provided for @brand_filter_price.
  ///
  /// In id, this message translates to:
  /// **'Harga'**
  String get brand_filter_price;

  /// No description provided for @brand_filter_rebate_rate.
  ///
  /// In id, this message translates to:
  /// **'Cashback'**
  String get brand_filter_rebate_rate;

  /// No description provided for @brand_filter_sales.
  ///
  /// In id, this message translates to:
  /// **'Penjualan'**
  String get brand_filter_sales;

  /// No description provided for @brand_filter_latest.
  ///
  /// In id, this message translates to:
  /// **'Baru'**
  String get brand_filter_latest;

  /// No description provided for @usage_guideline_title.
  ///
  /// In id, this message translates to:
  /// **'Panduan Pengguna'**
  String get usage_guideline_title;

  /// No description provided for @usage_guideline_description.
  ///
  /// In id, this message translates to:
  /// **'Dapatkan cashback di Shopee/TikTok dalam 3 langkah mudah!'**
  String get usage_guideline_description;

  /// No description provided for @usage_guideline_step1.
  ///
  /// In id, this message translates to:
  /// **'Buka produk di <red>Shopee</red> atau <red>TikTok</red>, salin tautan produk'**
  String get usage_guideline_step1;

  /// No description provided for @usage_guideline_step2.
  ///
  /// In id, this message translates to:
  /// **'Buka <red>GENCO</red>, tempel tautan, lihat cashback'**
  String get usage_guideline_step2;

  /// No description provided for @usage_guideline_step3.
  ///
  /// In id, this message translates to:
  /// **'Klik ke <red>Shopee</red>/<red>TikTok</red> via GENCO, selesaikan pembelian'**
  String get usage_guideline_step3;

  /// No description provided for @brand_home_top_title.
  ///
  /// In id, this message translates to:
  /// **'Brand Pilihan <red>Premium</red>'**
  String get brand_home_top_title;

  /// No description provided for @brand_home_top_subtitle.
  ///
  /// In id, this message translates to:
  /// **'Temukan favoritmu dengan harga terbaik & cashback!'**
  String get brand_home_top_subtitle;

  /// No description provided for @brand_tiktok_hot_sale_title.
  ///
  /// In id, this message translates to:
  /// **'Brand Populer di TikTok'**
  String get brand_tiktok_hot_sale_title;

  /// No description provided for @brand_high_rebate_title.
  ///
  /// In id, this message translates to:
  /// **'Brand Cashback Tinggi'**
  String get brand_high_rebate_title;

  /// No description provided for @brand_highest_rebate_rate.
  ///
  /// In id, this message translates to:
  /// **'Cashback Tertinggi'**
  String get brand_highest_rebate_rate;

  /// No description provided for @message_no_data.
  ///
  /// In id, this message translates to:
  /// **'Belum ada data'**
  String get message_no_data;

  /// No description provided for @income_pre_total_income.
  ///
  /// In id, this message translates to:
  /// **'Pendapatan Diterima'**
  String get income_pre_total_income;

  /// No description provided for @income_today.
  ///
  /// In id, this message translates to:
  /// **'Pendapatan Hari Ini'**
  String get income_today;

  /// No description provided for @income_amount_to_be_credited.
  ///
  /// In id, this message translates to:
  /// **'Menunggu Pembayaran'**
  String get income_amount_to_be_credited;

  /// No description provided for @income_amount_to_be_credited_hint.
  ///
  /// In id, this message translates to:
  /// **'Termasuk cashback pesanan yang belum dikonfirmasi. Hanya perkiraan.'**
  String get income_amount_to_be_credited_hint;

  /// No description provided for @income_amount_credited.
  ///
  /// In id, this message translates to:
  /// **'Dana Masuk'**
  String get income_amount_credited;

  /// No description provided for @income_amount_credited_description.
  ///
  /// In id, this message translates to:
  /// **'Jumlah yang sudah diterima'**
  String get income_amount_credited_description;

  /// No description provided for @income_amount_available_for_withdrawal.
  ///
  /// In id, this message translates to:
  /// **'Saldo yang dapat ditarik'**
  String get income_amount_available_for_withdrawal;

  /// No description provided for @income_withdrawal_button.
  ///
  /// In id, this message translates to:
  /// **'Tarik Dana'**
  String get income_withdrawal_button;

  /// No description provided for @income_withdrawal_success.
  ///
  /// In id, this message translates to:
  /// **'Penarikan Berhasil!'**
  String get income_withdrawal_success;

  /// No description provided for @income_withdrawal_failed.
  ///
  /// In id, this message translates to:
  /// **'Penarikan Gagal'**
  String get income_withdrawal_failed;

  /// No description provided for @income_withdrawal_amount.
  ///
  /// In id, this message translates to:
  /// **'Jumlah Penarikan'**
  String get income_withdrawal_amount;

  /// No description provided for @income_withdrawal_amount_hint.
  ///
  /// In id, this message translates to:
  /// **'Masukkan jumlah penarikan'**
  String get income_withdrawal_amount_hint;

  /// No description provided for @income_transaction_history.
  ///
  /// In id, this message translates to:
  /// **'Riwayat Transaksi'**
  String get income_transaction_history;

  /// No description provided for @income_transaction_history_empty.
  ///
  /// In id, this message translates to:
  /// **'Belum ada transaksi'**
  String get income_transaction_history_empty;

  /// No description provided for @income_transaction_detail.
  ///
  /// In id, this message translates to:
  /// **'Detail Transaksi'**
  String get income_transaction_detail;

  /// No description provided for @income_my_order.
  ///
  /// In id, this message translates to:
  /// **'Pesanan Saya'**
  String get income_my_order;

  /// No description provided for @income_income_detail.
  ///
  /// In id, this message translates to:
  /// **'Detail Pendapatan'**
  String get income_income_detail;

  /// No description provided for @income_pre_total_income_description.
  ///
  /// In id, this message translates to:
  /// **'Perkiraan total pendapatan sebagai referensi. Jumlah akhir akan disesuaikan dengan pembayaran aktual'**
  String get income_pre_total_income_description;

  /// No description provided for @ok.
  ///
  /// In id, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @finish.
  ///
  /// In id, this message translates to:
  /// **'Selesai'**
  String get finish;

  /// No description provided for @credited_rebase_income.
  ///
  /// In id, this message translates to:
  /// **'Cashback Diterima'**
  String get credited_rebase_income;

  /// No description provided for @all.
  ///
  /// In id, this message translates to:
  /// **'Semua'**
  String get all;

  /// No description provided for @income.
  ///
  /// In id, this message translates to:
  /// **'Pemasukan'**
  String get income;

  /// No description provided for @expenditure.
  ///
  /// In id, this message translates to:
  /// **'Pengeluaran'**
  String get expenditure;

  /// No description provided for @rebase_income.
  ///
  /// In id, this message translates to:
  /// **'Cashback Masuk'**
  String get rebase_income;

  /// No description provided for @rebase_expenditure.
  ///
  /// In id, this message translates to:
  /// **'Cashback Keluar'**
  String get rebase_expenditure;

  /// No description provided for @withdrawal_account.
  ///
  /// In id, this message translates to:
  /// **'Akun Penarikan'**
  String get withdrawal_account;

  /// No description provided for @please_select_withdrawal_account.
  ///
  /// In id, this message translates to:
  /// **'Pilih akun penarikan'**
  String get please_select_withdrawal_account;

  /// No description provided for @withdrawal_amount.
  ///
  /// In id, this message translates to:
  /// **'Jumlah maksimum yang bisa ditarik'**
  String get withdrawal_amount;

  /// No description provided for @withdrawal_amount_hint.
  ///
  /// In id, this message translates to:
  /// **'Jumlah minimum yang bisa ditarik'**
  String get withdrawal_amount_hint;

  /// No description provided for @withdrawal_amount_min.
  ///
  /// In id, this message translates to:
  /// **'Minimal penarikan'**
  String get withdrawal_amount_min;

  /// No description provided for @withdrawal_all.
  ///
  /// In id, this message translates to:
  /// **'Tarik Semua'**
  String get withdrawal_all;

  /// No description provided for @withdrawal_finish.
  ///
  /// In id, this message translates to:
  /// **'Penarikan Selesai'**
  String get withdrawal_finish;

  /// No description provided for @withdrawal_success.
  ///
  /// In id, this message translates to:
  /// **'Penarikan berhasil diajukan!'**
  String get withdrawal_success;

  /// No description provided for @withdrawal_success_hint.
  ///
  /// In id, this message translates to:
  /// **'Dana masuk ≤24 jam kerja. Cek saldo akun Anda!'**
  String get withdrawal_success_hint;

  /// No description provided for @withdrawal_failed.
  ///
  /// In id, this message translates to:
  /// **'Penarikan Gagal'**
  String get withdrawal_failed;

  /// No description provided for @withdrawal_fees.
  ///
  /// In id, this message translates to:
  /// **'Biaya Admin'**
  String get withdrawal_fees;

  /// No description provided for @withdrawal_fees_hint.
  ///
  /// In id, this message translates to:
  /// **'Biaya 1.5% dari penarikan. Minimal Rp5.550. Jika <Rp5.550, biaya tetap Rp5.550.'**
  String get withdrawal_fees_hint;

  /// No description provided for @withdrawal_hint.
  ///
  /// In id, this message translates to:
  /// **'Catatan'**
  String get withdrawal_hint;

  /// No description provided for @withdrawal_hint_description.
  ///
  /// In id, this message translates to:
  /// **'Dana masuk ≤24 jam kerja (selain Sabtu/Minggu/libur)'**
  String get withdrawal_hint_description;

  /// No description provided for @trade_type.
  ///
  /// In id, this message translates to:
  /// **'Jenis Transaksi'**
  String get trade_type;

  /// No description provided for @trade_time.
  ///
  /// In id, this message translates to:
  /// **'Waktu'**
  String get trade_time;

  /// No description provided for @trade_serial_number.
  ///
  /// In id, this message translates to:
  /// **'Nomor Transaksi'**
  String get trade_serial_number;

  /// No description provided for @trade_channel.
  ///
  /// In id, this message translates to:
  /// **'Channel'**
  String get trade_channel;

  /// No description provided for @trade_order_number.
  ///
  /// In id, this message translates to:
  /// **'ID Pesanan'**
  String get trade_order_number;

  /// No description provided for @income_order_rebate.
  ///
  /// In id, this message translates to:
  /// **'Cashback Pesanan'**
  String get income_order_rebate;

  /// No description provided for @income_campaign_reward.
  ///
  /// In id, this message translates to:
  /// **'Hadiah Event'**
  String get income_campaign_reward;

  /// No description provided for @income_expected_total_amount.
  ///
  /// In id, this message translates to:
  /// **'Total Estimasi Cashback'**
  String get income_expected_total_amount;

  /// No description provided for @income_expected_total_amount_hint.
  ///
  /// In id, this message translates to:
  /// **'Estimasi cashback pesanan dibayar. Dapat berubah jika ada refund.'**
  String get income_expected_total_amount_hint;

  /// No description provided for @income_actual_credited_amount.
  ///
  /// In id, this message translates to:
  /// **'Jumlah Cashback Sebenarnya'**
  String get income_actual_credited_amount;

  /// No description provided for @income_actual_credited_amount_hint.
  ///
  /// In id, this message translates to:
  /// **'Sudah Masuk ke Akun'**
  String get income_actual_credited_amount_hint;

  /// No description provided for @order_title.
  ///
  /// In id, this message translates to:
  /// **'Pesanan Saya'**
  String get order_title;

  /// No description provided for @order_tab_all.
  ///
  /// In id, this message translates to:
  /// **'Semua'**
  String get order_tab_all;

  /// No description provided for @order_tab_processing.
  ///
  /// In id, this message translates to:
  /// **'Diproses'**
  String get order_tab_processing;

  /// No description provided for @order_tab_completed.
  ///
  /// In id, this message translates to:
  /// **'Selesai'**
  String get order_tab_completed;

  /// No description provided for @order_tab_expired.
  ///
  /// In id, this message translates to:
  /// **'Kadaluarsa'**
  String get order_tab_expired;

  /// No description provided for @order_application_time.
  ///
  /// In id, this message translates to:
  /// **'Waktu permintaan:'**
  String get order_application_time;

  /// No description provided for @order_status_processing.
  ///
  /// In id, this message translates to:
  /// **'Diproses'**
  String get order_status_processing;

  /// No description provided for @order_status_completed.
  ///
  /// In id, this message translates to:
  /// **'Selesai'**
  String get order_status_completed;

  /// No description provided for @order_status_expired.
  ///
  /// In id, this message translates to:
  /// **'Kadaluarsa'**
  String get order_status_expired;

  /// No description provided for @order_expected_cashback.
  ///
  /// In id, this message translates to:
  /// **'Estimasi cashback:'**
  String get order_expected_cashback;

  /// No description provided for @order_cashback_info.
  ///
  /// In id, this message translates to:
  /// **'Ketentuan Cashback'**
  String get order_cashback_info;

  /// No description provided for @rebate_instruction_title.
  ///
  /// In id, this message translates to:
  /// **'Syarat Cashback'**
  String get rebate_instruction_title;

  /// No description provided for @rebate_instruction_content.
  ///
  /// In id, this message translates to:
  /// **'Cashback masuk ke saldo GENCO setelah pesanan dikonfirmasi di Shopee/TikTok. Proses verifikasi: 1-3 hari.'**
  String get rebate_instruction_content;

  /// No description provided for @rebate_step_1_title.
  ///
  /// In id, this message translates to:
  /// **'01'**
  String get rebate_step_1_title;

  /// No description provided for @rebate_step_1_content.
  ///
  /// In id, this message translates to:
  /// **'Pesan via Shopee/TikTok lewat link GENCO.'**
  String get rebate_step_1_content;

  /// No description provided for @rebate_step_2_title.
  ///
  /// In id, this message translates to:
  /// **'02'**
  String get rebate_step_2_title;

  /// No description provided for @rebate_step_2_content.
  ///
  /// In id, this message translates to:
  /// **'GENCO verifikasi otomatis (1-3 hari).'**
  String get rebate_step_2_content;

  /// No description provided for @rebate_step_3_title.
  ///
  /// In id, this message translates to:
  /// **'03'**
  String get rebate_step_3_title;

  /// No description provided for @rebate_step_3_content.
  ///
  /// In id, this message translates to:
  /// **'Cashback masuk setelah pesanan SELESAI.'**
  String get rebate_step_3_content;

  /// No description provided for @rebate_how_to_get_title.
  ///
  /// In id, this message translates to:
  /// **'Cara Dapatkan Cashback:'**
  String get rebate_how_to_get_title;

  /// No description provided for @rebate_how_to_order_title.
  ///
  /// In id, this message translates to:
  /// **'CARA BELI'**
  String get rebate_how_to_order_title;

  /// No description provided for @rebate_how_to_order_content.
  ///
  /// In id, this message translates to:
  /// **'Setiap produk HARUS diakses lewat GENCO sebelum checkout.'**
  String get rebate_how_to_order_content;

  /// No description provided for @rebate_genco_last_app_title.
  ///
  /// In id, this message translates to:
  /// **'GENCO HARUS App Terakhir'**
  String get rebate_genco_last_app_title;

  /// No description provided for @rebate_genco_last_app_content.
  ///
  /// In id, this message translates to:
  /// **'Cashback tidak dapat dilacak jika membuka aplikasi lain setelah GENCO'**
  String get rebate_genco_last_app_content;

  /// No description provided for @rebate_unsupported_order_title.
  ///
  /// In id, this message translates to:
  /// **'Pesanan Tidak Memenuhi Syarat'**
  String get rebate_unsupported_order_title;

  /// No description provided for @rebate_unsupported_order_content.
  ///
  /// In id, this message translates to:
  /// **'Pesanan di luar tautan GENCO tidak memenuhi syarat.'**
  String get rebate_unsupported_order_content;

  /// No description provided for @rebase_info.
  ///
  /// In id, this message translates to:
  /// **'Detail Cashback'**
  String get rebase_info;

  /// No description provided for @rebase_price.
  ///
  /// In id, this message translates to:
  /// **'Harga Beli'**
  String get rebase_price;

  /// No description provided for @rebase_rate.
  ///
  /// In id, this message translates to:
  /// **'Persentase Cashback'**
  String get rebase_rate;

  /// No description provided for @rebase_cash.
  ///
  /// In id, this message translates to:
  /// **'Cashback yang Diperoleh'**
  String get rebase_cash;

  /// No description provided for @withdrawal_choose_method.
  ///
  /// In id, this message translates to:
  /// **'Pilih Akun Penarikan'**
  String get withdrawal_choose_method;

  /// No description provided for @withdrawal_add_card.
  ///
  /// In id, this message translates to:
  /// **'+ Rekening Bank'**
  String get withdrawal_add_card;

  /// No description provided for @withdrawal_add_e_card.
  ///
  /// In id, this message translates to:
  /// **'+ Tambah E-Wallet'**
  String get withdrawal_add_e_card;

  /// No description provided for @name.
  ///
  /// In id, this message translates to:
  /// **'Nama'**
  String get name;

  /// No description provided for @name_placeholder.
  ///
  /// In id, this message translates to:
  /// **'Nama sesuai rekening'**
  String get name_placeholder;

  /// No description provided for @bank_name.
  ///
  /// In id, this message translates to:
  /// **'Nama Bank'**
  String get bank_name;

  /// No description provided for @select_bank.
  ///
  /// In id, this message translates to:
  /// **'Pilih Bank'**
  String get select_bank;

  /// No description provided for @bank_card_number.
  ///
  /// In id, this message translates to:
  /// **'Nomor Rekening'**
  String get bank_card_number;

  /// No description provided for @input_bank_card_number.
  ///
  /// In id, this message translates to:
  /// **'Masukkan nomor rekening'**
  String get input_bank_card_number;

  /// No description provided for @search.
  ///
  /// In id, this message translates to:
  /// **'Cari'**
  String get search;

  /// No description provided for @button_next.
  ///
  /// In id, this message translates to:
  /// **'Lanjut'**
  String get button_next;

  /// No description provided for @bind_bank_card_confirm.
  ///
  /// In id, this message translates to:
  /// **'Konfirmasi Kartu'**
  String get bind_bank_card_confirm;

  /// No description provided for @e_wallet.
  ///
  /// In id, this message translates to:
  /// **'E-Wallet'**
  String get e_wallet;

  /// No description provided for @phone_number.
  ///
  /// In id, this message translates to:
  /// **'Nomor Telepon'**
  String get phone_number;

  /// No description provided for @phone_number_placeholder.
  ///
  /// In id, this message translates to:
  /// **'Masukkan Nomor Telepon'**
  String get phone_number_placeholder;

  /// No description provided for @select_e_wallet.
  ///
  /// In id, this message translates to:
  /// **'Pilih E-Wallet'**
  String get select_e_wallet;

  /// No description provided for @usage_hint.
  ///
  /// In id, this message translates to:
  /// **'Hemat dengan GENCO!'**
  String get usage_hint;

  /// No description provided for @my_collection.
  ///
  /// In id, this message translates to:
  /// **'Favorit Saya'**
  String get my_collection;

  /// No description provided for @guide_step1_title.
  ///
  /// In id, this message translates to:
  /// **'4 Langkah Dapat Cashback'**
  String get guide_step1_title;

  /// No description provided for @guide_step1_content_flow_1.
  ///
  /// In id, this message translates to:
  /// **'Buka Shopee/TikTok, salin tautan produk'**
  String get guide_step1_content_flow_1;

  /// No description provided for @guide_step1_content_flow_2.
  ///
  /// In id, this message translates to:
  /// **'Buka GENCO, cek cashback'**
  String get guide_step1_content_flow_2;

  /// No description provided for @guide_step1_content_flow_3.
  ///
  /// In id, this message translates to:
  /// **'Klik ke Shopee/TikTok, beli'**
  String get guide_step1_content_flow_3;

  /// No description provided for @guide_step1_content_flow_4.
  ///
  /// In id, this message translates to:
  /// **'Cashback masuk setelah selesai'**
  String get guide_step1_content_flow_4;

  /// No description provided for @guide_step1_content_flow_1_title.
  ///
  /// In id, this message translates to:
  /// **'SALIN TAUTAN PRODUK'**
  String get guide_step1_content_flow_1_title;

  /// No description provided for @guide_step1_content_flow_1_description.
  ///
  /// In id, this message translates to:
  /// **'Di Shopee/TikTok: Klik \'Bagikan\' > \'Salin Tautan\'.'**
  String get guide_step1_content_flow_1_description;

  /// No description provided for @guide_step1_content_flow_2_title.
  ///
  /// In id, this message translates to:
  /// **'CEK CASHBACK DI GENCO'**
  String get guide_step1_content_flow_2_title;

  /// No description provided for @guide_step1_content_flow_2_description.
  ///
  /// In id, this message translates to:
  /// **'Buka GENCO > Tempel otomatis atau ketik manual.'**
  String get guide_step1_content_flow_2_description;

  /// No description provided for @guide_step1_content_flow_3_title.
  ///
  /// In id, this message translates to:
  /// **'BELI VIA GENCO'**
  String get guide_step1_content_flow_3_title;

  /// No description provided for @guide_step1_content_flow_3_description.
  ///
  /// In id, this message translates to:
  /// **'Klik \'Beli di Shopee/TikTok\' > Checkout seperti biasa.'**
  String get guide_step1_content_flow_3_description;

  /// No description provided for @guide_step1_content_flow_4_title.
  ///
  /// In id, this message translates to:
  /// **'DAPATKAN CASHBACK'**
  String get guide_step1_content_flow_4_title;

  /// No description provided for @guide_step1_content_flow_4_description.
  ///
  /// In id, this message translates to:
  /// **'Cashback otomatis masuk setelah klik \'Pesanan Selesai\' di Shopee/TikTok.'**
  String get guide_step1_content_flow_4_description;

  /// No description provided for @guide_step1_content_flow_5_title.
  ///
  /// In id, this message translates to:
  /// **'Panduan Video'**
  String get guide_step1_content_flow_5_title;

  /// No description provided for @guide_step1_content_flow_5_description.
  ///
  /// In id, this message translates to:
  /// **'Tonton video cara klaim cashback!'**
  String get guide_step1_content_flow_5_description;

  /// No description provided for @feedback_cash.
  ///
  /// In id, this message translates to:
  /// **'Feedback Cashback'**
  String get feedback_cash;

  /// No description provided for @edit.
  ///
  /// In id, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @cancel.
  ///
  /// In id, this message translates to:
  /// **'Batal'**
  String get cancel;

  /// No description provided for @select_all.
  ///
  /// In id, this message translates to:
  /// **'Pilih Semua'**
  String get select_all;

  /// No description provided for @cancel_select_all.
  ///
  /// In id, this message translates to:
  /// **'Batalkan Pilihan'**
  String get cancel_select_all;

  /// No description provided for @delete.
  ///
  /// In id, this message translates to:
  /// **'Hapus'**
  String get delete;

  /// No description provided for @setting.
  ///
  /// In id, this message translates to:
  /// **'Pengaturan'**
  String get setting;

  /// No description provided for @my_avatar.
  ///
  /// In id, this message translates to:
  /// **'Foto Profil'**
  String get my_avatar;

  /// No description provided for @nickname.
  ///
  /// In id, this message translates to:
  /// **'Nama Panggilan'**
  String get nickname;

  /// No description provided for @whatsapp_account.
  ///
  /// In id, this message translates to:
  /// **'Akun WhatsApp'**
  String get whatsapp_account;

  /// No description provided for @modify_phone_number.
  ///
  /// In id, this message translates to:
  /// **'Ubah Nomor Ponsel'**
  String get modify_phone_number;

  /// No description provided for @modify_password.
  ///
  /// In id, this message translates to:
  /// **'Ubah Kata Sandi'**
  String get modify_password;

  /// No description provided for @privacy.
  ///
  /// In id, this message translates to:
  /// **'Privasi'**
  String get privacy;

  /// No description provided for @about.
  ///
  /// In id, this message translates to:
  /// **'Tentang'**
  String get about;

  /// No description provided for @modify_nickname.
  ///
  /// In id, this message translates to:
  /// **'Ubah Nickname'**
  String get modify_nickname;

  /// No description provided for @login_with_tiktok.
  ///
  /// In id, this message translates to:
  /// **'Masuk dengan TikTok'**
  String get login_with_tiktok;

  /// No description provided for @login_title.
  ///
  /// In id, this message translates to:
  /// **'Selamat Datang di GENCO'**
  String get login_title;

  /// No description provided for @login_subtitle.
  ///
  /// In id, this message translates to:
  /// **'Beli Hemat,\nBagikan Dapat Duit'**
  String get login_subtitle;

  /// No description provided for @whatsapp_account_hint.
  ///
  /// In id, this message translates to:
  /// **'Masukkan nomor WhatsApp Anda'**
  String get whatsapp_account_hint;

  /// No description provided for @next_step.
  ///
  /// In id, this message translates to:
  /// **'Lanjut'**
  String get next_step;

  /// No description provided for @account_empty_hint.
  ///
  /// In id, this message translates to:
  /// **'Nomor WhatsApp tidak boleh kosong'**
  String get account_empty_hint;

  /// No description provided for @input_opt_verification_code.
  ///
  /// In id, this message translates to:
  /// **'Masukkan Kode OTP'**
  String get input_opt_verification_code;

  /// Nomor WhatsApp penerima kode OTP
  ///
  /// In id, this message translates to:
  /// **'Kode verifikasi dikirim ke WhatsApp {phone}'**
  String input_opt_verification_code_hint(String phone);

  /// No description provided for @login_with_password.
  ///
  /// In id, this message translates to:
  /// **'Masuk dengan Sandi'**
  String get login_with_password;

  /// No description provided for @resend_in.
  ///
  /// In id, this message translates to:
  /// **'Kirim Ulang dalam'**
  String get resend_in;

  /// No description provided for @seconds.
  ///
  /// In id, this message translates to:
  /// **' detik'**
  String get seconds;

  /// No description provided for @resend_code.
  ///
  /// In id, this message translates to:
  /// **'Kirim Ulang'**
  String get resend_code;

  /// No description provided for @login.
  ///
  /// In id, this message translates to:
  /// **'Masuk'**
  String get login;

  /// No description provided for @input_opt_verification_code_error.
  ///
  /// In id, this message translates to:
  /// **'Harap masukkan kode OTP'**
  String get input_opt_verification_code_error;

  /// No description provided for @login_success.
  ///
  /// In id, this message translates to:
  /// **'Berhasil Masuk'**
  String get login_success;

  /// No description provided for @welcome_back.
  ///
  /// In id, this message translates to:
  /// **'Hai, selamat datang kembali!'**
  String get welcome_back;

  /// No description provided for @please_input_your_password.
  ///
  /// In id, this message translates to:
  /// **'Masukkan kata sandi Anda'**
  String get please_input_your_password;

  /// No description provided for @login_password.
  ///
  /// In id, this message translates to:
  /// **'Kata Sandi'**
  String get login_password;

  /// No description provided for @input_password_hint.
  ///
  /// In id, this message translates to:
  /// **'Kata sandi Anda'**
  String get input_password_hint;

  /// No description provided for @login_with_verification_code.
  ///
  /// In id, this message translates to:
  /// **'Masuk dengan Kode OTP'**
  String get login_with_verification_code;

  /// No description provided for @login_agreement.
  ///
  /// In id, this message translates to:
  /// **'Dengan masuk, Anda menyetujui'**
  String get login_agreement;

  /// No description provided for @and.
  ///
  /// In id, this message translates to:
  /// **'dan'**
  String get and;

  /// No description provided for @user_agreement.
  ///
  /// In id, this message translates to:
  /// **'Syarat Penggunaan'**
  String get user_agreement;

  /// No description provided for @privacy_policy.
  ///
  /// In id, this message translates to:
  /// **'Kebijakan Privasi'**
  String get privacy_policy;

  /// No description provided for @setting_login_password.
  ///
  /// In id, this message translates to:
  /// **'Atur Kata Sandi'**
  String get setting_login_password;

  /// No description provided for @set_password_hint.
  ///
  /// In id, this message translates to:
  /// **'Sandi: 6-20 karakter (huruf + angka)'**
  String get set_password_hint;

  /// No description provided for @login_password_confirm.
  ///
  /// In id, this message translates to:
  /// **'Konfirmasi Sandi'**
  String get login_password_confirm;

  /// No description provided for @login_password_confirm_hint.
  ///
  /// In id, this message translates to:
  /// **'Masukkan kembali sandi'**
  String get login_password_confirm_hint;

  /// No description provided for @password_not_same.
  ///
  /// In id, this message translates to:
  /// **'Sandi tidak cocok'**
  String get password_not_same;

  /// No description provided for @modify_success.
  ///
  /// In id, this message translates to:
  /// **'Berhasil Diubah'**
  String get modify_success;

  /// No description provided for @find_product.
  ///
  /// In id, this message translates to:
  /// **'Temukan Produk'**
  String get find_product;

  /// No description provided for @check_cash_back.
  ///
  /// In id, this message translates to:
  /// **'Cek Cashback'**
  String get check_cash_back;

  /// No description provided for @back.
  ///
  /// In id, this message translates to:
  /// **'Kembali'**
  String get back;

  /// No description provided for @can_not_open_link.
  ///
  /// In id, this message translates to:
  /// **'Tautan Tidak Bisa Dibuka'**
  String get can_not_open_link;

  /// No description provided for @collection.
  ///
  /// In id, this message translates to:
  /// **'Favorit'**
  String get collection;

  /// No description provided for @order_right_now.
  ///
  /// In id, this message translates to:
  /// **'Beli Sekarang'**
  String get order_right_now;

  /// No description provided for @add_to_collection_success.
  ///
  /// In id, this message translates to:
  /// **'Ditambahkan ke Favorit'**
  String get add_to_collection_success;

  /// No description provided for @please_select_bank_or_e_wallet.
  ///
  /// In id, this message translates to:
  /// **'Pilih Bank/Dompet Digital'**
  String get please_select_bank_or_e_wallet;

  /// No description provided for @please_input_amount.
  ///
  /// In id, this message translates to:
  /// **'Masukkan jumlah penarikan'**
  String get please_input_amount;

  /// No description provided for @please_select_bank.
  ///
  /// In id, this message translates to:
  /// **'Pilih bank'**
  String get please_select_bank;

  /// No description provided for @please_input_bank_number.
  ///
  /// In id, this message translates to:
  /// **'Masukkan nomor rekening'**
  String get please_input_bank_number;

  /// No description provided for @please_select_e_wallet.
  ///
  /// In id, this message translates to:
  /// **'Pilih dompet digital'**
  String get please_select_e_wallet;

  /// No description provided for @please_input_e_wallet_account.
  ///
  /// In id, this message translates to:
  /// **'Masukkan akun dompet digital'**
  String get please_input_e_wallet_account;

  /// No description provided for @logout.
  ///
  /// In id, this message translates to:
  /// **'Keluar'**
  String get logout;

  /// No description provided for @logout_confirm_title.
  ///
  /// In id, this message translates to:
  /// **'Konfirmasi Keluar'**
  String get logout_confirm_title;

  /// No description provided for @logout_confirm_message.
  ///
  /// In id, this message translates to:
  /// **'Yakin ingin keluar dari akun?'**
  String get logout_confirm_message;

  /// No description provided for @confirm.
  ///
  /// In id, this message translates to:
  /// **'OK'**
  String get confirm;

  /// No description provided for @jump_to_tiktok.
  ///
  /// In id, this message translates to:
  /// **'Segera dialihkan ke TikTok'**
  String get jump_to_tiktok;

  /// 分享产品的文本
  ///
  /// In id, this message translates to:
  /// **'Segera pesan melalui GENCO untuk memperoleh estimasi cashback sebesar Rp{amount}.'**
  String share_text(String amount);

  /// No description provided for @nickname_hint.
  ///
  /// In id, this message translates to:
  /// **'Silakan masukkan nama panggilan'**
  String get nickname_hint;

  /// No description provided for @member_introduction.
  ///
  /// In id, this message translates to:
  /// **'Tingkatkan menjadi agen atau mitra untuk menghasilkan lebih banyak'**
  String get member_introduction;

  /// No description provided for @member_level_state.
  ///
  /// In id, this message translates to:
  /// **'Status Level'**
  String get member_level_state;

  /// No description provided for @member_status_description.
  ///
  /// In id, this message translates to:
  /// **'Anda saat ini belum menjadi agen atau mitra'**
  String get member_status_description;

  /// No description provided for @member_level_silver_agent.
  ///
  /// In id, this message translates to:
  /// **'Agen Perak'**
  String get member_level_silver_agent;

  /// No description provided for @member_level_partner.
  ///
  /// In id, this message translates to:
  /// **'Mitra'**
  String get member_level_partner;

  /// No description provided for @member_level_silver_agent_fee.
  ///
  /// In id, this message translates to:
  /// **'Biaya Agen Perak'**
  String get member_level_silver_agent_fee;

  /// No description provided for @member_level_partner_fee.
  ///
  /// In id, this message translates to:
  /// **'Biaya Mitra'**
  String get member_level_partner_fee;

  /// No description provided for @become_member.
  ///
  /// In id, this message translates to:
  /// **'Menjadi Agen'**
  String get become_member;

  /// No description provided for @year.
  ///
  /// In id, this message translates to:
  /// **'Tahun'**
  String get year;

  /// No description provided for @delete_account.
  ///
  /// In id, this message translates to:
  /// **'Nonaktifkan Akun'**
  String get delete_account;

  /// No description provided for @delete_account_title.
  ///
  /// In id, this message translates to:
  /// **'Konfirmasi Penghapusan Akun?'**
  String get delete_account_title;

  /// No description provided for @delete_account_content.
  ///
  /// In id, this message translates to:
  /// **'Setelah akun dihapus, semua hak dan manfaat terkait Anda akan hilang secara permanen,\ndan tidak akan bisa masuk lagi ke akun ini.'**
  String get delete_account_content;

  /// No description provided for @product_link_empty.
  ///
  /// In id, this message translates to:
  /// **'Tidak menemukan produk untuk link tersebut'**
  String get product_link_empty;

  /// No description provided for @product_link_empty_content.
  ///
  /// In id, this message translates to:
  /// **'Tidak ditemukan produk yang sesuai untuk tautan produk ini. Harap periksa apakah tautannya benar, atau coba produk lain.'**
  String get product_link_empty_content;

  /// No description provided for @exclusive_benefits.
  ///
  /// In id, this message translates to:
  /// **'Manfaat Eksklusif'**
  String get exclusive_benefits;

  /// No description provided for @member_benefits_silver_agent_1.
  ///
  /// In id, this message translates to:
  /// **'Masa Berlaku'**
  String get member_benefits_silver_agent_1;

  /// No description provided for @member_benefits_silver_agent_1_value.
  ///
  /// In id, this message translates to:
  /// **'1 Tahun'**
  String get member_benefits_silver_agent_1_value;

  /// No description provided for @member_benefits_silver_agent_2.
  ///
  /// In id, this message translates to:
  /// **'Bonus Undangan'**
  String get member_benefits_silver_agent_2;

  /// No description provided for @member_benefits_silver_agent_2_value.
  ///
  /// In id, this message translates to:
  /// **'Undang berhasil, dapatkan hadiah tambahan'**
  String get member_benefits_silver_agent_2_value;

  /// No description provided for @member_benefits_silver_agent_3.
  ///
  /// In id, this message translates to:
  /// **'Komisi Belanja Tim'**
  String get member_benefits_silver_agent_3;

  /// No description provided for @member_benefits_silver_agent_3_value.
  ///
  /// In id, this message translates to:
  /// **'Belanja tim, Anda dapatkan keuntungan komisi'**
  String get member_benefits_silver_agent_3_value;

  /// No description provided for @member_benefits_silver_agent_4.
  ///
  /// In id, this message translates to:
  /// **'Cashback Tambahan'**
  String get member_benefits_silver_agent_4;

  /// No description provided for @member_benefits_silver_agent_4_value.
  ///
  /// In id, this message translates to:
  /// **'Cashback lebih tinggi, hingga 50%'**
  String get member_benefits_silver_agent_4_value;

  /// No description provided for @member_benefits_silver_agent_5.
  ///
  /// In id, this message translates to:
  /// **'Cashback Tanpa Batas'**
  String get member_benefits_silver_agent_5;

  /// No description provided for @member_benefits_silver_agent_5_value.
  ///
  /// In id, this message translates to:
  /// **'Cashback tanpa batas'**
  String get member_benefits_silver_agent_5_value;

  /// No description provided for @member_benefits_partner_agent_1.
  ///
  /// In id, this message translates to:
  /// **'Masa Berlaku'**
  String get member_benefits_partner_agent_1;

  /// No description provided for @member_benefits_partner_agent_1_value.
  ///
  /// In id, this message translates to:
  /// **'Permanen'**
  String get member_benefits_partner_agent_1_value;

  /// No description provided for @member_benefits_partner_agent_2.
  ///
  /// In id, this message translates to:
  /// **'Bonus Undangan'**
  String get member_benefits_partner_agent_2;

  /// No description provided for @member_benefits_partner_agent_2_value.
  ///
  /// In id, this message translates to:
  /// **'Dapatkan bonus tinggi 10 miliar+ jika berhasil'**
  String get member_benefits_partner_agent_2_value;

  /// No description provided for @member_benefits_partner_agent_3.
  ///
  /// In id, this message translates to:
  /// **'Komisi Belanja Tim'**
  String get member_benefits_partner_agent_3;

  /// No description provided for @member_benefits_partner_agent_3_value.
  ///
  /// In id, this message translates to:
  /// **'Dapatkan hingga 20% cashback dari setiap cashback jaringan bawah'**
  String get member_benefits_partner_agent_3_value;

  /// No description provided for @member_benefits_partner_agent_4.
  ///
  /// In id, this message translates to:
  /// **'Cashback Tambahan'**
  String get member_benefits_partner_agent_4;

  /// No description provided for @member_benefits_partner_agent_4_value.
  ///
  /// In id, this message translates to:
  /// **'Cashback lebih tinggi, hingga 100%'**
  String get member_benefits_partner_agent_4_value;

  /// No description provided for @role.
  ///
  /// In id, this message translates to:
  /// **'Peran'**
  String get role;

  /// No description provided for @benefit.
  ///
  /// In id, this message translates to:
  /// **'Manfaat'**
  String get benefit;

  /// No description provided for @normal_user.
  ///
  /// In id, this message translates to:
  /// **'Pengguna Biasa'**
  String get normal_user;

  /// No description provided for @normal_user_2_benefit.
  ///
  /// In id, this message translates to:
  /// **'Tidak ada bonus'**
  String get normal_user_2_benefit;

  /// No description provided for @agent.
  ///
  /// In id, this message translates to:
  /// **'Agen'**
  String get agent;

  /// No description provided for @member_benefits_silver_agent_2_benefit.
  ///
  /// In id, this message translates to:
  /// **'Bisa dapatkan bonus hingga Rp 10.000.000'**
  String get member_benefits_silver_agent_2_benefit;

  /// No description provided for @normal_user_3_benefit.
  ///
  /// In id, this message translates to:
  /// **'Tidak ada komisi belanja'**
  String get normal_user_3_benefit;

  /// No description provided for @member_benefits_silver_agent_3_benefit.
  ///
  /// In id, this message translates to:
  /// **'Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) '**
  String get member_benefits_silver_agent_3_benefit;

  /// No description provided for @normal_user_4_benefit.
  ///
  /// In id, this message translates to:
  /// **'Tidak ada komisi belanja'**
  String get normal_user_4_benefit;

  /// No description provided for @member_benefits_silver_agent_4_benefit.
  ///
  /// In id, this message translates to:
  /// **'Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) '**
  String get member_benefits_silver_agent_4_benefit;

  /// No description provided for @normal_user_5_benefit.
  ///
  /// In id, this message translates to:
  /// **'Tidak ada komisi belanja'**
  String get normal_user_5_benefit;

  /// No description provided for @member_benefits_silver_agent_5_benefit.
  ///
  /// In id, this message translates to:
  /// **'Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) '**
  String get member_benefits_silver_agent_5_benefit;

  /// No description provided for @order_payment.
  ///
  /// In id, this message translates to:
  /// **'Pembayaran Pesanan'**
  String get order_payment;

  /// No description provided for @order_price.
  ///
  /// In id, this message translates to:
  /// **'Jumlah Pesanan'**
  String get order_price;

  /// No description provided for @product_name.
  ///
  /// In id, this message translates to:
  /// **'Nama Produk'**
  String get product_name;

  /// No description provided for @real_payment_price.
  ///
  /// In id, this message translates to:
  /// **'Jumlah Pembayaran Aktual'**
  String get real_payment_price;

  /// No description provided for @agent_fee.
  ///
  /// In id, this message translates to:
  /// **'Biaya Agen'**
  String get agent_fee;

  /// No description provided for @purchase_right_now.
  ///
  /// In id, this message translates to:
  /// **'Beli Sekarang'**
  String get purchase_right_now;

  /// No description provided for @payment_problem.
  ///
  /// In id, this message translates to:
  /// **'Masalah Pembayaran'**
  String get payment_problem;

  /// No description provided for @payment_complete.
  ///
  /// In id, this message translates to:
  /// **'Pembayaran Selesai'**
  String get payment_complete;

  /// No description provided for @payment_agreement.
  ///
  /// In id, this message translates to:
  /// **'Baca dan Setujui'**
  String get payment_agreement;

  /// No description provided for @payment_agreement_link.
  ///
  /// In id, this message translates to:
  /// **'Syarat Pembayaran'**
  String get payment_agreement_link;

  /// No description provided for @cashback_is_0.
  ///
  /// In id, this message translates to:
  /// **'Produk ini tidak ada cashback'**
  String get cashback_is_0;

  /// No description provided for @cashback_is_0_content.
  ///
  /// In id, this message translates to:
  /// **'Produk ini tidak ada cashback. Apakah Anda ingin melanjutkan?'**
  String get cashback_is_0_content;

  /// No description provided for @nickname_too_long.
  ///
  /// In id, this message translates to:
  /// **'Nama panggilan terlalu panjang, maksimal 10 karakter'**
  String get nickname_too_long;

  /// No description provided for @check_payment_result.
  ///
  /// In id, this message translates to:
  /// **'Memeriksa Hasil Pembayaran'**
  String get check_payment_result;

  /// No description provided for @payment_amount.
  ///
  /// In id, this message translates to:
  /// **'Jumlah Pembayaran'**
  String get payment_amount;

  /// No description provided for @payment_id.
  ///
  /// In id, this message translates to:
  /// **'Nomor Transaksi Pembayaran'**
  String get payment_id;

  /// No description provided for @payment_method.
  ///
  /// In id, this message translates to:
  /// **'Metode Pembayaran'**
  String get payment_method;

  /// No description provided for @payment_success.
  ///
  /// In id, this message translates to:
  /// **'Pembayaran Sukses'**
  String get payment_success;

  /// No description provided for @payment_failed.
  ///
  /// In id, this message translates to:
  /// **'Pembayaran Gagal'**
  String get payment_failed;

  /// No description provided for @level_status.
  ///
  /// In id, this message translates to:
  /// **'Status Level'**
  String get level_status;

  /// No description provided for @valid_for.
  ///
  /// In id, this message translates to:
  /// **'Berlaku: 1 tahun'**
  String get valid_for;

  /// No description provided for @upgrade_date.
  ///
  /// In id, this message translates to:
  /// **'Tanggal Upgrade: 11 Juni 2025'**
  String get upgrade_date;

  /// No description provided for @to_gold_progress.
  ///
  /// In id, this message translates to:
  /// **'Progres Menjadi Agen Emas'**
  String get to_gold_progress;

  /// No description provided for @invite_to_upgrade.
  ///
  /// In id, this message translates to:
  /// **'Undang 10 teman untuk menjadi Agen Perak atau lebih tinggi'**
  String get invite_to_upgrade;

  /// No description provided for @silver_agent.
  ///
  /// In id, this message translates to:
  /// **'Agen Perak'**
  String get silver_agent;

  /// No description provided for @gold_agent.
  ///
  /// In id, this message translates to:
  /// **'Agen Emas'**
  String get gold_agent;

  /// No description provided for @diamond_agent.
  ///
  /// In id, this message translates to:
  /// **'Agen Berlian'**
  String get diamond_agent;

  /// No description provided for @partner.
  ///
  /// In id, this message translates to:
  /// **'Mitra'**
  String get partner;

  /// No description provided for @direct_invite_reward.
  ///
  /// In id, this message translates to:
  /// **'Hadiah Undangan Langsung'**
  String get direct_invite_reward;

  /// No description provided for @direct_invite_detail.
  ///
  /// In id, this message translates to:
  /// **'Undang 1 agen dan dapatkan hadiah Rp 35.000. Undang 3 orang untuk balik modal!'**
  String get direct_invite_detail;

  /// No description provided for @team_purchase_bonus.
  ///
  /// In id, this message translates to:
  /// **'Bonus Belanja Tim'**
  String get team_purchase_bonus;

  /// No description provided for @team_purchase_detail.
  ///
  /// In id, this message translates to:
  /// **'Dapatkan keuntungan 10% dari total belanja tim yang kamu undang langsung'**
  String get team_purchase_detail;

  /// No description provided for @team_purchase_detail_gold.
  ///
  /// In id, this message translates to:
  /// **'Undangan tidak langsung (tingkat dua): masing-masing dapat terkumpul Rp 15.000; Undangan tidak langsung (tingkat tiga): masing-masing dapat terkumpul Rp 10.000.'**
  String get team_purchase_detail_gold;

  /// No description provided for @training.
  ///
  /// In id, this message translates to:
  /// **'Pelatihan'**
  String get training;

  /// No description provided for @training_detail.
  ///
  /// In id, this message translates to:
  /// **'Mentor profesional memberikan kursus berkualitas tinggi dan layanan bimbingan menyeluruh'**
  String get training_detail;

  /// No description provided for @extra_cashback.
  ///
  /// In id, this message translates to:
  /// **'Cashback Ekstra'**
  String get extra_cashback;

  /// No description provided for @extra_cashback_detail_gold.
  ///
  /// In id, this message translates to:
  /// **'Tiap kembangkan 10 Agen Emas: Hadiah Rp 300.000'**
  String get extra_cashback_detail_gold;

  /// No description provided for @extra_cashback_detail.
  ///
  /// In id, this message translates to:
  /// **'Nikmati manfaat cashback ekstra selama periode tertentu'**
  String get extra_cashback_detail;

  /// No description provided for @invite_to_upgrade_empty.
  ///
  /// In id, this message translates to:
  /// **'Undang 10 teman baru untuk bergabung menjadi Agen Perak atau level lebih tinggi.\\nAnda akan ter-upgrade status secara otomatis!'**
  String get invite_to_upgrade_empty;

  /// No description provided for @silver_partner.
  ///
  /// In id, this message translates to:
  /// **'Mitra Perak'**
  String get silver_partner;

  /// No description provided for @gold_partner.
  ///
  /// In id, this message translates to:
  /// **'Mitra Emas'**
  String get gold_partner;

  /// No description provided for @diamond_partner.
  ///
  /// In id, this message translates to:
  /// **'Mitra Berlian'**
  String get diamond_partner;

  /// No description provided for @partner_extra_bonus1.
  ///
  /// In id, this message translates to:
  /// **'Tiap kembangkan 10 Mitra Perak: Hadiah Rp 1.000.000'**
  String get partner_extra_bonus1;

  /// No description provided for @partner_extra_bonus2.
  ///
  /// In id, this message translates to:
  /// **'Tiap kembangkan 10 Mitra Emas: Hadiah Rp 2.000.000'**
  String get partner_extra_bonus2;

  /// No description provided for @direct_invite_detail2.
  ///
  /// In id, this message translates to:
  /// **'Tiap undang 1 agen, hadiah Rp 200.000, undang 3 orang balik modal seketika!'**
  String get direct_invite_detail2;

  /// No description provided for @team_purchase_detail_gold2.
  ///
  /// In id, this message translates to:
  /// **'Undangan tidak langsung (tingkat dua): masing-masing dapat Rp 100.000; Undangan tidak langsung (tingkat tiga): masing-masing dapat Rp 50.000'**
  String get team_purchase_detail_gold2;

  /// No description provided for @extra_bonus.
  ///
  /// In id, this message translates to:
  /// **'Bonus Ekstra'**
  String get extra_bonus;

  /// No description provided for @normal_member.
  ///
  /// In id, this message translates to:
  /// **'Member Biasa'**
  String get normal_member;

  /// No description provided for @high_cashback.
  ///
  /// In id, this message translates to:
  /// **'Cashback Tinggi'**
  String get high_cashback;

  /// No description provided for @high_cashback_description.
  ///
  /// In id, this message translates to:
  /// **'Semakin banyak belanja, semakin banyak hemat'**
  String get high_cashback_description;

  /// No description provided for @no_limit.
  ///
  /// In id, this message translates to:
  /// **'Tanpa Batas'**
  String get no_limit;

  /// No description provided for @no_limit_description.
  ///
  /// In id, this message translates to:
  /// **'Nikmati cashback tanpa batas'**
  String get no_limit_description;

  /// No description provided for @user_service.
  ///
  /// In id, this message translates to:
  /// **'Layanan Pelanggan'**
  String get user_service;

  /// No description provided for @user_service_description.
  ///
  /// In id, this message translates to:
  /// **'Layanan pelanggan berkualitas'**
  String get user_service_description;

  /// No description provided for @invite_and_eran_bonus.
  ///
  /// In id, this message translates to:
  /// **'Undang dan Dapatkan Bonus'**
  String get invite_and_eran_bonus;

  /// No description provided for @invite_code.
  ///
  /// In id, this message translates to:
  /// **'Kode Undangan'**
  String get invite_code;

  /// No description provided for @input_invite_code.
  ///
  /// In id, this message translates to:
  /// **'Masukkan Kode Undangan'**
  String get input_invite_code;

  /// No description provided for @contact_up.
  ///
  /// In id, this message translates to:
  /// **'Hubungi Atasan'**
  String get contact_up;

  /// No description provided for @congratulation_to_add_group.
  ///
  /// In id, this message translates to:
  /// **'Selamat Anda bergabung dengan'**
  String get congratulation_to_add_group;

  /// No description provided for @group.
  ///
  /// In id, this message translates to:
  /// **' tim'**
  String get group;

  /// No description provided for @my_team.
  ///
  /// In id, this message translates to:
  /// **'Tim Saya'**
  String get my_team;

  /// No description provided for @task_center.
  ///
  /// In id, this message translates to:
  /// **'Pusat Tugas'**
  String get task_center;

  /// No description provided for @task_center_title.
  ///
  /// In id, this message translates to:
  /// **'Pusat Tugas'**
  String get task_center_title;

  /// No description provided for @task_cash_income.
  ///
  /// In id, this message translates to:
  /// **'Pendapatan Tunai (Rp)'**
  String get task_cash_income;

  /// No description provided for @task_withdraw.
  ///
  /// In id, this message translates to:
  /// **'Tarik'**
  String get task_withdraw;

  /// No description provided for @task_withdrawable_amount.
  ///
  /// In id, this message translates to:
  /// **'Jumlah yang Dapat Ditarik'**
  String get task_withdrawable_amount;

  /// No description provided for @task_daily_tasks.
  ///
  /// In id, this message translates to:
  /// **'Daftar Tugas'**
  String get task_daily_tasks;

  /// No description provided for @task_invite_reward.
  ///
  /// In id, this message translates to:
  /// **'Hadiah Pesanan Pertama Undangan'**
  String get task_invite_reward;

  /// No description provided for @task_invite_progress.
  ///
  /// In id, this message translates to:
  /// **'Progres Undangan'**
  String get task_invite_progress;

  /// No description provided for @task_order_progress.
  ///
  /// In id, this message translates to:
  /// **'Progres Pesanan'**
  String get task_order_progress;

  /// No description provided for @task_invite_count.
  ///
  /// In id, this message translates to:
  /// **'Jumlah Undangan'**
  String get task_invite_count;

  /// No description provided for @task_order_count.
  ///
  /// In id, this message translates to:
  /// **'Jumlah Pesanan'**
  String get task_order_count;

  /// No description provided for @task_conditions_met.
  ///
  /// In id, this message translates to:
  /// **'Kondisi Terpenuhi'**
  String get task_conditions_met;

  /// No description provided for @task_conditions_not_met.
  ///
  /// In id, this message translates to:
  /// **'Kondisi Tidak Terpenuhi'**
  String get task_conditions_not_met;

  /// No description provided for @task_go_claim.
  ///
  /// In id, this message translates to:
  /// **'Klaim'**
  String get task_go_claim;

  /// No description provided for @task_feature_developing.
  ///
  /// In id, this message translates to:
  /// **'Fitur Sedang Dikembangkan'**
  String get task_feature_developing;

  /// No description provided for @task_developing.
  ///
  /// In id, this message translates to:
  /// **'Silakan selesaikan tugas undangan dan pesanan pertama sebelum mengklaim hadiah'**
  String get task_developing;

  /// No description provided for @task_return_cash_welfare.
  ///
  /// In id, this message translates to:
  /// **'Kesejahteraan Uang Kembali'**
  String get task_return_cash_welfare;

  /// No description provided for @task_return_cash_welfare_desc.
  ///
  /// In id, this message translates to:
  /// **'Eksklusif Harian'**
  String get task_return_cash_welfare_desc;

  /// No description provided for @task_view_record.
  ///
  /// In id, this message translates to:
  /// **'Lihat Catatan'**
  String get task_view_record;

  /// No description provided for @task_record_title.
  ///
  /// In id, this message translates to:
  /// **'Catatan Tugas'**
  String get task_record_title;

  /// No description provided for @task_total_invites.
  ///
  /// In id, this message translates to:
  /// **'Total Undangan'**
  String get task_total_invites;

  /// No description provided for @task_redeemed_invites.
  ///
  /// In id, this message translates to:
  /// **'Undangan Ditukar'**
  String get task_redeemed_invites;

  /// No description provided for @task_total_orders.
  ///
  /// In id, this message translates to:
  /// **'Total Pesanan'**
  String get task_total_orders;

  /// No description provided for @task_redeemed_orders.
  ///
  /// In id, this message translates to:
  /// **'Pesanan Ditukar'**
  String get task_redeemed_orders;

  /// No description provided for @task_close.
  ///
  /// In id, this message translates to:
  /// **'Tutup'**
  String get task_close;

  /// No description provided for @task_reward_amount.
  ///
  /// In id, this message translates to:
  /// **'Jumlah Hadiah'**
  String get task_reward_amount;

  /// No description provided for @task_per_completion.
  ///
  /// In id, this message translates to:
  /// **'Per Penyelesaian'**
  String get task_per_completion;

  /// No description provided for @task_banner_title.
  ///
  /// In id, this message translates to:
  /// **'Cek'**
  String get task_banner_title;

  /// No description provided for @task_banner_subtitle.
  ///
  /// In id, this message translates to:
  /// **'Pendapatanmu Disini!'**
  String get task_banner_subtitle;

  /// No description provided for @task_banner_line1.
  ///
  /// In id, this message translates to:
  /// **'Semakin banyak transaksi dan undang'**
  String get task_banner_line1;

  /// No description provided for @task_banner_line2.
  ///
  /// In id, this message translates to:
  /// **'teman, semakin banyak juga'**
  String get task_banner_line2;

  /// No description provided for @task_banner_line3.
  ///
  /// In id, this message translates to:
  /// **'pendapatanmu.'**
  String get task_banner_line3;

  /// No description provided for @invite_bonus.
  ///
  /// In id, this message translates to:
  /// **'Bonus Undangan'**
  String get invite_bonus;

  /// No description provided for @shopping_bonus.
  ///
  /// In id, this message translates to:
  /// **'Bonus Belanja'**
  String get shopping_bonus;

  /// No description provided for @cumulative_number_of_invitations.
  ///
  /// In id, this message translates to:
  /// **'Jumlah Undangan Kumulatif'**
  String get cumulative_number_of_invitations;

  /// No description provided for @today.
  ///
  /// In id, this message translates to:
  /// **'Hari Ini'**
  String get today;

  /// No description provided for @invite_agent.
  ///
  /// In id, this message translates to:
  /// **'Undang Agen'**
  String get invite_agent;

  /// No description provided for @invite_normal_user.
  ///
  /// In id, this message translates to:
  /// **'Undang Pengguna Biasa'**
  String get invite_normal_user;

  /// No description provided for @silver.
  ///
  /// In id, this message translates to:
  /// **'Perak'**
  String get silver;

  /// No description provided for @gold.
  ///
  /// In id, this message translates to:
  /// **'Emas'**
  String get gold;

  /// No description provided for @diamond.
  ///
  /// In id, this message translates to:
  /// **'Berlian'**
  String get diamond;

  /// No description provided for @team_support.
  ///
  /// In id, this message translates to:
  /// **'Kontribusi Tim'**
  String get team_support;

  /// No description provided for @received_bonus.
  ///
  /// In id, this message translates to:
  /// **'Bonus yang Diterima'**
  String get received_bonus;

  /// No description provided for @invite_time.
  ///
  /// In id, this message translates to:
  /// **'Waktu Undangan'**
  String get invite_time;

  /// No description provided for @pre_team_cashback.
  ///
  /// In id, this message translates to:
  /// **'Perkiraan Cashback Kontribusi Tim'**
  String get pre_team_cashback;

  /// No description provided for @invite_and_earn_money.
  ///
  /// In id, this message translates to:
  /// **'Undang dan Hasilkan Uang'**
  String get invite_and_earn_money;

  /// No description provided for @level_up_schedule.
  ///
  /// In id, this message translates to:
  /// **'Jadwal Naik Level'**
  String get level_up_schedule;

  /// No description provided for @level_up_description.
  ///
  /// In id, this message translates to:
  /// **'Penjelasan Naik Level'**
  String get level_up_description;

  /// No description provided for @level_up_content_title.
  ///
  /// In id, this message translates to:
  /// **'Undang 10 teman menjadi Agen Perak atau level lebih tinggi'**
  String get level_up_content_title;

  /// No description provided for @level_up_description_title.
  ///
  /// In id, this message translates to:
  /// **'IDR Di Tangan'**
  String get level_up_description_title;

  /// No description provided for @level_up_description_title1.
  ///
  /// In id, this message translates to:
  /// **'Cukup kembangkan 10 Agen Berlian!'**
  String get level_up_description_title1;

  /// No description provided for @invite_gold_agent.
  ///
  /// In id, this message translates to:
  /// **'Undang Agen Emas'**
  String get invite_gold_agent;

  /// No description provided for @invite_diamond_agent.
  ///
  /// In id, this message translates to:
  /// **'Undang Agen Berlian'**
  String get invite_diamond_agent;

  /// No description provided for @level_up_bonus.
  ///
  /// In id, this message translates to:
  /// **'Progres dan Hadiah Naik Level'**
  String get level_up_bonus;

  /// No description provided for @activity_rule.
  ///
  /// In id, this message translates to:
  /// **'Aturan Aktivitas'**
  String get activity_rule;

  /// No description provided for @bonus.
  ///
  /// In id, this message translates to:
  /// **'Hadiah'**
  String get bonus;

  /// No description provided for @direct_invite_detail3.
  ///
  /// In id, this message translates to:
  /// **'Undang agen, hadiah Rp 35.000 / orang'**
  String get direct_invite_detail3;

  /// No description provided for @team_bonus.
  ///
  /// In id, this message translates to:
  /// **'Bonus Tim'**
  String get team_bonus;

  /// No description provided for @team_bonus_detail.
  ///
  /// In id, this message translates to:
  /// **'Rekomendasi tidak langsung (tingkat dua): setiap orang terkumpul Rp 100.000;\nRekomendasi tidak langsung (tingkat tiga): setiap orang terkumpul Rp 50.000;\nJika Anda mencapai status “Mitra Emas” (berhasil merekomendasikan langsung 10 mitra), hadiah dapat ditarik, berlaku selama 60 hari sejak hadiah dihasilkan. Jika tidak mencapai dalam waktu tersebut, hadiah akan hangus secara otomatis.'**
  String get team_bonus_detail;

  /// No description provided for @partner_extra_bonus3.
  ///
  /// In id, this message translates to:
  /// **'Tiap kembangkan 10 Mitra Emas: Hadiah Rp 2.000.000;\nTiap kembangkan 10 Mitra Berlian: Hadiah Rp 100.000.000.'**
  String get partner_extra_bonus3;

  /// No description provided for @invite_code_empty_hint.
  ///
  /// In id, this message translates to:
  /// **'Kode undangan tidak boleh kosong!'**
  String get invite_code_empty_hint;

  /// No description provided for @copy_success.
  ///
  /// In id, this message translates to:
  /// **'Berhasil Disalin'**
  String get copy_success;

  /// No description provided for @network_is_not_available.
  ///
  /// In id, this message translates to:
  /// **'Jaringan tidak tersedia, silakan periksa koneksi Anda'**
  String get network_is_not_available;

  /// No description provided for @login_with_other_method.
  ///
  /// In id, this message translates to:
  /// **'Masuk dengan metode lain'**
  String get login_with_other_method;

  /// No description provided for @member_introduction_level_silver_agent.
  ///
  /// In id, this message translates to:
  /// **'Silver'**
  String get member_introduction_level_silver_agent;

  /// No description provided for @normal_member_user.
  ///
  /// In id, this message translates to:
  /// **'Normal'**
  String get normal_member_user;

  /// No description provided for @agree_with_payment_term.
  ///
  /// In id, this message translates to:
  /// **'Apakah Anda setuju dengan syarat pembayaran?'**
  String get agree_with_payment_term;

  /// No description provided for @please_choose_payment_method.
  ///
  /// In id, this message translates to:
  /// **'Silakan pilih metode pembayaran'**
  String get please_choose_payment_method;

  /// No description provided for @qrcode.
  ///
  /// In id, this message translates to:
  /// **'KODE QR'**
  String get qrcode;

  /// No description provided for @open_payment_link.
  ///
  /// In id, this message translates to:
  /// **'Buka Tautan Pembayaran Langsung'**
  String get open_payment_link;

  /// No description provided for @pay_with_qrcode.
  ///
  /// In id, this message translates to:
  /// **'Bayar dengan QR Code'**
  String get pay_with_qrcode;

  /// No description provided for @pay_with_qrcode_usage.
  ///
  /// In id, this message translates to:
  /// **'Scan kode QR untuk membuka tautan pembayaran dan melakukan pembayaran. Jika ingin membayar langsung di aplikasi ini, ketuk untuk membuka tautannya.'**
  String get pay_with_qrcode_usage;

  /// No description provided for @jump_link_failed.
  ///
  /// In id, this message translates to:
  /// **'Gagal Membuka Tautan'**
  String get jump_link_failed;

  /// No description provided for @login_expired_hint.
  ///
  /// In id, this message translates to:
  /// **'Login telah kedaluwarsa, silakan login kembali!'**
  String get login_expired_hint;

  /// No description provided for @network_error.
  ///
  /// In id, this message translates to:
  /// **'Kesalahan jaringan'**
  String get network_error;

  /// No description provided for @unknown_error.
  ///
  /// In id, this message translates to:
  /// **'Kesalahan tidak diketahui'**
  String get unknown_error;
}

class _SDelegate extends LocalizationsDelegate<S> {
  const _SDelegate();

  @override
  Future<S> load(Locale locale) {
    return SynchronousFuture<S>(lookupS(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'id', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_SDelegate old) => false;
}

S lookupS(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return SEn();
    case 'id':
      return SId();
    case 'zh':
      return SZh();
  }

  throw FlutterError(
    'S.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
