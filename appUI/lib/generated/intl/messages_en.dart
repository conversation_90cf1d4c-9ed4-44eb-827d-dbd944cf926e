// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(phone) => "Sent to WhatsApp ${phone}";

  static String m1(amount) =>
      "Place your order immediately through GENCO to receive an estimated cashback of Rp ${amount}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about": MessageLookupByLibrary.simpleMessage("About"),
    "account_empty_hint": MessageLookupByLibrary.simpleMessage(
      "Account required",
    ),
    "activity_rule": MessageLookupByLibrary.simpleMessage("Activity Rules"),
    "add_to_collection_success": MessageLookupByLibrary.simpleMessage(
      "Added to Collections",
    ),
    "agent": MessageLookupByLibrary.simpleMessage("Agent"),
    "agent_fee": MessageLookupByLibrary.simpleMessage("Agent Fee"),
    "agree_with_payment_term": MessageLookupByLibrary.simpleMessage(
      "Do you agree with the payment terms?",
    ),
    "all": MessageLookupByLibrary.simpleMessage("All"),
    "and": MessageLookupByLibrary.simpleMessage("and"),
    "back": MessageLookupByLibrary.simpleMessage("Back"),
    "bank_card_number": MessageLookupByLibrary.simpleMessage("Card Number"),
    "bank_name": MessageLookupByLibrary.simpleMessage("Bank Name"),
    "become_member": MessageLookupByLibrary.simpleMessage("Become Agent"),
    "benefit": MessageLookupByLibrary.simpleMessage("Benefit"),
    "bind_bank_card_confirm": MessageLookupByLibrary.simpleMessage(
      "Confirm Card",
    ),
    "bonus": MessageLookupByLibrary.simpleMessage("Reward"),
    "brand_filter_all": MessageLookupByLibrary.simpleMessage("All"),
    "brand_filter_latest": MessageLookupByLibrary.simpleMessage("New"),
    "brand_filter_price": MessageLookupByLibrary.simpleMessage("Price"),
    "brand_filter_rebate_rate": MessageLookupByLibrary.simpleMessage(
      "Cashback Rate",
    ),
    "brand_filter_sales": MessageLookupByLibrary.simpleMessage("Sales"),
    "brand_high_rebate_title": MessageLookupByLibrary.simpleMessage(
      "High Cashback Brands",
    ),
    "brand_highest_rebate_rate": MessageLookupByLibrary.simpleMessage(
      "Highest Cashback Rate",
    ),
    "brand_home_top_subtitle": MessageLookupByLibrary.simpleMessage(
      "Discover favorites while enjoying great shopping",
    ),
    "brand_home_top_title": MessageLookupByLibrary.simpleMessage(
      "<red>Carefully Selected</red> Brands",
    ),
    "brand_tiktok_hot_sale_title": MessageLookupByLibrary.simpleMessage(
      "TikTok Hot Brands",
    ),
    "button_next": MessageLookupByLibrary.simpleMessage("Continue"),
    "can_not_open_link": MessageLookupByLibrary.simpleMessage(
      "Can\'t open link",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cancel_select_all": MessageLookupByLibrary.simpleMessage("Deselect All"),
    "cashback_is_0": MessageLookupByLibrary.simpleMessage(
      "This product has no cashback",
    ),
    "cashback_is_0_content": MessageLookupByLibrary.simpleMessage(
      "There is no cashback for this product. Do you want to continue?",
    ),
    "check_cash_back": MessageLookupByLibrary.simpleMessage("View Cashback"),
    "check_payment_result": MessageLookupByLibrary.simpleMessage(
      "Checking Payment Result",
    ),
    "collection": MessageLookupByLibrary.simpleMessage("Collection"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "congratulation_to_add_group": MessageLookupByLibrary.simpleMessage(
      "Congratulations on joining",
    ),
    "contact_up": MessageLookupByLibrary.simpleMessage("Contact Supervisor"),
    "copy_success": MessageLookupByLibrary.simpleMessage("Copied Successfully"),
    "credited_rebase_income": MessageLookupByLibrary.simpleMessage(
      "Cashback Income",
    ),
    "cumulative_number_of_invitations": MessageLookupByLibrary.simpleMessage(
      "Total Invitations",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "delete_account": MessageLookupByLibrary.simpleMessage(
      "Deactivate Account",
    ),
    "delete_account_content": MessageLookupByLibrary.simpleMessage(
      "After account deletion, all your privileges and benefits will be permanently lost,\nand you will permanently lose access to your account.",
    ),
    "delete_account_title": MessageLookupByLibrary.simpleMessage(
      "Confirm Account Deletion?",
    ),
    "detail_brand_product_amount": MessageLookupByLibrary.simpleMessage(
      "products",
    ),
    "detail_brand_product_amount_pre": MessageLookupByLibrary.simpleMessage(
      "Total",
    ),
    "detail_cashback_amount": MessageLookupByLibrary.simpleMessage(
      "Estimated Rebate Amount",
    ),
    "detail_cashback_flow_check": MessageLookupByLibrary.simpleMessage(
      "View Tutorial",
    ),
    "detail_cashback_flow_step1": MessageLookupByLibrary.simpleMessage(
      "Click Product",
    ),
    "detail_cashback_flow_step2": MessageLookupByLibrary.simpleMessage(
      "Order on\nTikTok",
    ),
    "detail_cashback_flow_step3": MessageLookupByLibrary.simpleMessage(
      "Track Order",
    ),
    "detail_cashback_flow_step4": MessageLookupByLibrary.simpleMessage(
      "Get Cashback",
    ),
    "detail_cashback_flow_title": MessageLookupByLibrary.simpleMessage(
      "Cashback Earning Process",
    ),
    "detail_price_title": MessageLookupByLibrary.simpleMessage("Price"),
    "detail_rebate_rate": MessageLookupByLibrary.simpleMessage("Rebate Rate"),
    "detail_sold": MessageLookupByLibrary.simpleMessage("Sold out"),
    "detail_sold_count": MessageLookupByLibrary.simpleMessage(""),
    "diamond": MessageLookupByLibrary.simpleMessage("Diamond"),
    "diamond_agent": MessageLookupByLibrary.simpleMessage("Diamond Agent"),
    "diamond_partner": MessageLookupByLibrary.simpleMessage("Diamond Partner"),
    "direct_invite_detail": MessageLookupByLibrary.simpleMessage(
      "Invite 1 agent to get 35,000 IDR reward. Invite 3 to break even!",
    ),
    "direct_invite_detail2": MessageLookupByLibrary.simpleMessage(
      "Reward 200,000 IDR per agent invited, break even by inviting just 3!",
    ),
    "direct_invite_detail3": MessageLookupByLibrary.simpleMessage(
      "Invite agents: 35,000 IDR reward per person",
    ),
    "direct_invite_reward": MessageLookupByLibrary.simpleMessage(
      "Direct Invitation Reward",
    ),
    "e_wallet": MessageLookupByLibrary.simpleMessage("E-Wallet"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "error_button_title": MessageLookupByLibrary.simpleMessage("Retry"),
    "error_title": MessageLookupByLibrary.simpleMessage(
      "Oops! Something went wrong. Please try again later.",
    ),
    "exclusive_benefits": MessageLookupByLibrary.simpleMessage(
      "Exclusive Benefits",
    ),
    "expenditure": MessageLookupByLibrary.simpleMessage("Expense"),
    "extra_bonus": MessageLookupByLibrary.simpleMessage("Extra Bonus"),
    "extra_cashback": MessageLookupByLibrary.simpleMessage("Extra Cashback"),
    "extra_cashback_detail": MessageLookupByLibrary.simpleMessage(
      "Enjoy extra cashback benefits during specified periods",
    ),
    "extra_cashback_detail_gold": MessageLookupByLibrary.simpleMessage(
      "For every 10 Gold Agents developed: 300,000 IDR reward",
    ),
    "feedback_cash": MessageLookupByLibrary.simpleMessage("Cashback"),
    "find_product": MessageLookupByLibrary.simpleMessage("Found Product"),
    "finish": MessageLookupByLibrary.simpleMessage("Complete"),
    "gold": MessageLookupByLibrary.simpleMessage("Gold"),
    "gold_agent": MessageLookupByLibrary.simpleMessage("Gold Agent"),
    "gold_partner": MessageLookupByLibrary.simpleMessage("Gold Partner"),
    "group": MessageLookupByLibrary.simpleMessage("\'s team"),
    "guide_step1_content_flow_1": MessageLookupByLibrary.simpleMessage(
      "Copy product link on Shopee/TikTok",
    ),
    "guide_step1_content_flow_1_description":
        MessageLookupByLibrary.simpleMessage(
          "Tap \'Share\' > \'Copy Link\' on Shopee/TikTok",
        ),
    "guide_step1_content_flow_1_title": MessageLookupByLibrary.simpleMessage(
      "Copy Product Link",
    ),
    "guide_step1_content_flow_2": MessageLookupByLibrary.simpleMessage(
      "Check cashback in GENCO",
    ),
    "guide_step1_content_flow_2_description":
        MessageLookupByLibrary.simpleMessage(
          "Paste link in GENCO to see cashback",
        ),
    "guide_step1_content_flow_2_title": MessageLookupByLibrary.simpleMessage(
      "Check Cashback in GENCO",
    ),
    "guide_step1_content_flow_3": MessageLookupByLibrary.simpleMessage(
      "Order on Shopee/TikTok",
    ),
    "guide_step1_content_flow_3_description":
        MessageLookupByLibrary.simpleMessage("Jump to Shopee/TikTok to buy"),
    "guide_step1_content_flow_3_title": MessageLookupByLibrary.simpleMessage(
      "Tap \'Order on Shopee/TikTok\'",
    ),
    "guide_step1_content_flow_4": MessageLookupByLibrary.simpleMessage(
      "Confirm order & get cashback",
    ),
    "guide_step1_content_flow_4_description":
        MessageLookupByLibrary.simpleMessage(
          "Cashback credited after order confirmation",
        ),
    "guide_step1_content_flow_4_title": MessageLookupByLibrary.simpleMessage(
      "Track & Get Cashback",
    ),
    "guide_step1_content_flow_5_description":
        MessageLookupByLibrary.simpleMessage(
          "Watch tutorial video for details",
        ),
    "guide_step1_content_flow_5_title": MessageLookupByLibrary.simpleMessage(
      "How to Get Cashback?",
    ),
    "guide_step1_title": MessageLookupByLibrary.simpleMessage(
      "4 Steps to Cashback",
    ),
    "high_cashback": MessageLookupByLibrary.simpleMessage("High Cashback"),
    "high_cashback_description": MessageLookupByLibrary.simpleMessage(
      "Spend more, save more",
    ),
    "home_cashback_button_title": MessageLookupByLibrary.simpleMessage(
      "Est. Cashback",
    ),
    "home_cashback_instructions_check_all":
        MessageLookupByLibrary.simpleMessage("Check All"),
    "home_cashback_instructions_step1": MessageLookupByLibrary.simpleMessage(
      "Copy product\nlink",
    ),
    "home_cashback_instructions_step2": MessageLookupByLibrary.simpleMessage(
      "Open GENCO\ncheck cashback",
    ),
    "home_cashback_instructions_step3": MessageLookupByLibrary.simpleMessage(
      "Go Shopee/\nTikTok",
    ),
    "home_cashback_instructions_step4": MessageLookupByLibrary.simpleMessage(
      "Get cashback",
    ),
    "home_cashback_instructions_title": MessageLookupByLibrary.simpleMessage(
      "Earn Cashback Process",
    ),
    "home_logo_slogan": MessageLookupByLibrary.simpleMessage(
      "Check & Earn Rebates",
    ),
    "home_navigation_brand": MessageLookupByLibrary.simpleMessage("Brand"),
    "home_navigation_home": MessageLookupByLibrary.simpleMessage("Home"),
    "home_navigation_income": MessageLookupByLibrary.simpleMessage("Income"),
    "home_navigation_mine": MessageLookupByLibrary.simpleMessage("Mine"),
    "home_platform_all": MessageLookupByLibrary.simpleMessage("All"),
    "home_platform_high_rebate": MessageLookupByLibrary.simpleMessage(
      "High Rebate",
    ),
    "home_platform_hot_sale": MessageLookupByLibrary.simpleMessage("Hottest"),
    "home_platform_shopee": MessageLookupByLibrary.simpleMessage("Shopee"),
    "home_platform_tiktok": MessageLookupByLibrary.simpleMessage("TikTok"),
    "home_rebate_rate_title": MessageLookupByLibrary.simpleMessage(
      "Cashback Rate",
    ),
    "home_search_button_title": MessageLookupByLibrary.simpleMessage("Paste"),
    "home_search_instructions_copy": MessageLookupByLibrary.simpleMessage(
      "Copy",
    ),
    "home_search_instructions_text": MessageLookupByLibrary.simpleMessage(
      "Open link in GENCO to get cashback",
    ),
    "home_search_placeholder": MessageLookupByLibrary.simpleMessage(
      "Copy product link, get cashback",
    ),
    "income": MessageLookupByLibrary.simpleMessage("Income"),
    "income_actual_credited_amount": MessageLookupByLibrary.simpleMessage(
      "Actual Cashback",
    ),
    "income_actual_credited_amount_hint": MessageLookupByLibrary.simpleMessage(
      "Credited to account",
    ),
    "income_amount_available_for_withdrawal":
        MessageLookupByLibrary.simpleMessage("Withdrawable Amount"),
    "income_amount_credited": MessageLookupByLibrary.simpleMessage(
      "Credited Amount",
    ),
    "income_amount_credited_description": MessageLookupByLibrary.simpleMessage(
      "Successfully credited amount",
    ),
    "income_amount_to_be_credited": MessageLookupByLibrary.simpleMessage(
      "Pending Credit",
    ),
    "income_amount_to_be_credited_hint": MessageLookupByLibrary.simpleMessage(
      "Includes unconfirmed order cashback. For reference only.",
    ),
    "income_campaign_reward": MessageLookupByLibrary.simpleMessage(
      "Campaign Reward",
    ),
    "income_expected_total_amount": MessageLookupByLibrary.simpleMessage(
      "Estimated Cashback",
    ),
    "income_expected_total_amount_hint": MessageLookupByLibrary.simpleMessage(
      "Estimated, subject to final credit",
    ),
    "income_income_detail": MessageLookupByLibrary.simpleMessage(
      "Income Details",
    ),
    "income_my_order": MessageLookupByLibrary.simpleMessage("My Orders"),
    "income_order_rebate": MessageLookupByLibrary.simpleMessage(
      "Order Cashback",
    ),
    "income_pre_total_income": MessageLookupByLibrary.simpleMessage(
      "Estimated Total Income",
    ),
    "income_pre_total_income_description": MessageLookupByLibrary.simpleMessage(
      "Estimated total income for reference. Final amount subject to actual payment.",
    ),
    "income_today": MessageLookupByLibrary.simpleMessage("Today\'s Income"),
    "income_transaction_detail": MessageLookupByLibrary.simpleMessage(
      "Transaction Details",
    ),
    "income_transaction_history": MessageLookupByLibrary.simpleMessage(
      "Transaction History",
    ),
    "income_transaction_history_empty": MessageLookupByLibrary.simpleMessage(
      "No transactions yet",
    ),
    "income_withdrawal_amount": MessageLookupByLibrary.simpleMessage(
      "Withdrawal Amount",
    ),
    "income_withdrawal_amount_hint": MessageLookupByLibrary.simpleMessage(
      "Enter amount",
    ),
    "income_withdrawal_button": MessageLookupByLibrary.simpleMessage(
      "Withdraw",
    ),
    "income_withdrawal_failed": MessageLookupByLibrary.simpleMessage(
      "Withdrawal Failed",
    ),
    "income_withdrawal_success": MessageLookupByLibrary.simpleMessage(
      "Withdrawal Success",
    ),
    "input_bank_card_number": MessageLookupByLibrary.simpleMessage(
      "Enter card number",
    ),
    "input_invite_code": MessageLookupByLibrary.simpleMessage(
      "Enter Invitation Code",
    ),
    "input_opt_verification_code": MessageLookupByLibrary.simpleMessage(
      "Enter OTP",
    ),
    "input_opt_verification_code_error": MessageLookupByLibrary.simpleMessage(
      "Enter code",
    ),
    "input_opt_verification_code_hint": m0,
    "input_password_hint": MessageLookupByLibrary.simpleMessage(
      "Enter password",
    ),
    "invite_agent": MessageLookupByLibrary.simpleMessage("Invite Agent"),
    "invite_and_earn_money": MessageLookupByLibrary.simpleMessage(
      "Invite and Earn Money",
    ),
    "invite_and_eran_bonus": MessageLookupByLibrary.simpleMessage(
      "Invite and Earn",
    ),
    "invite_bonus": MessageLookupByLibrary.simpleMessage("Referral Bonus"),
    "invite_code": MessageLookupByLibrary.simpleMessage("Invitation Code"),
    "invite_code_empty_hint": MessageLookupByLibrary.simpleMessage(
      "Invitation code cannot be empty!",
    ),
    "invite_diamond_agent": MessageLookupByLibrary.simpleMessage(
      "Invite Diamond Agent",
    ),
    "invite_gold_agent": MessageLookupByLibrary.simpleMessage(
      "Invite Gold Agent",
    ),
    "invite_normal_user": MessageLookupByLibrary.simpleMessage(
      "Invite Regular User",
    ),
    "invite_time": MessageLookupByLibrary.simpleMessage("Invitation Time"),
    "invite_to_upgrade": MessageLookupByLibrary.simpleMessage(
      "Invite 10 friends to become Silver Agents or higher",
    ),
    "invite_to_upgrade_empty": MessageLookupByLibrary.simpleMessage(
      "Invite 10 new friends to become Silver Agents or higher.\\nYour status will upgrade automatically!",
    ),
    "jump_link_failed": MessageLookupByLibrary.simpleMessage(
      "Jump Link Failed",
    ),
    "jump_to_tiktok": MessageLookupByLibrary.simpleMessage(
      "Will jump to TikTok",
    ),
    "level_status": MessageLookupByLibrary.simpleMessage("Level Status"),
    "level_up_bonus": MessageLookupByLibrary.simpleMessage(
      "Level Up Progress & Rewards",
    ),
    "level_up_content_title": MessageLookupByLibrary.simpleMessage(
      "Invite 10 friends to become Silver Agents or higher",
    ),
    "level_up_description": MessageLookupByLibrary.simpleMessage(
      "Upgrade Instructions",
    ),
    "level_up_description_title": MessageLookupByLibrary.simpleMessage(
      "IDR in Hand",
    ),
    "level_up_description_title1": MessageLookupByLibrary.simpleMessage(
      "Just develop 10 Diamond Agents!",
    ),
    "level_up_schedule": MessageLookupByLibrary.simpleMessage(
      "Level Up Progress",
    ),
    "loading_more_empty": MessageLookupByLibrary.simpleMessage(
      "No wallpaper yet ~",
    ),
    "loading_more_error": MessageLookupByLibrary.simpleMessage(
      "Loading failed, try again",
    ),
    "loading_more_no_more": MessageLookupByLibrary.simpleMessage(
      "Oh it\'s over!",
    ),
    "loading_more_retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "loading_more_write": MessageLookupByLibrary.simpleMessage(
      "write a rating",
    ),
    "login": MessageLookupByLibrary.simpleMessage("Login"),
    "login_agreement": MessageLookupByLibrary.simpleMessage(
      "By logging in, you agree to the",
    ),
    "login_button": MessageLookupByLibrary.simpleMessage("Login"),
    "login_code_hint": MessageLookupByLibrary.simpleMessage(
      "Enter the verification code",
    ),
    "login_code_label": MessageLookupByLibrary.simpleMessage(
      "Verification Code",
    ),
    "login_code_seconds": MessageLookupByLibrary.simpleMessage("s"),
    "login_code_sent": MessageLookupByLibrary.simpleMessage(
      "Verification code sent",
    ),
    "login_enter_phone": MessageLookupByLibrary.simpleMessage(
      "Please enter phone number",
    ),
    "login_enter_phone_code": MessageLookupByLibrary.simpleMessage(
      "Please enter phone number and code",
    ),
    "login_expired_hint": MessageLookupByLibrary.simpleMessage(
      "Login has expired, please log in again!",
    ),
    "login_failed": MessageLookupByLibrary.simpleMessage("Login failed"),
    "login_get_code": MessageLookupByLibrary.simpleMessage("Get Code"),
    "login_mobile_subtitle": MessageLookupByLibrary.simpleMessage(
      "Please login with your phone number",
    ),
    "login_mobile_title": MessageLookupByLibrary.simpleMessage("Phone Login"),
    "login_password": MessageLookupByLibrary.simpleMessage("Password"),
    "login_password_alternative": MessageLookupByLibrary.simpleMessage(
      "Login with Password",
    ),
    "login_password_confirm": MessageLookupByLibrary.simpleMessage(
      "Confirm Password",
    ),
    "login_password_confirm_hint": MessageLookupByLibrary.simpleMessage(
      "Re-enter password",
    ),
    "login_phone_hint": MessageLookupByLibrary.simpleMessage(
      "Enter your phone number",
    ),
    "login_phone_label": MessageLookupByLibrary.simpleMessage("Phone Number"),
    "login_send_failed": MessageLookupByLibrary.simpleMessage(
      "Failed to send code",
    ),
    "login_subtitle": MessageLookupByLibrary.simpleMessage(
      "Save on shopping,\nEarn by sharing",
    ),
    "login_success": MessageLookupByLibrary.simpleMessage("Login Success"),
    "login_title": MessageLookupByLibrary.simpleMessage("Welcome to GENCO"),
    "login_welcome_back": MessageLookupByLibrary.simpleMessage("Welcome back"),
    "login_with_other_method": MessageLookupByLibrary.simpleMessage(
      "Login with other method",
    ),
    "login_with_password": MessageLookupByLibrary.simpleMessage(
      "Password Login",
    ),
    "login_with_tiktok": MessageLookupByLibrary.simpleMessage(
      "Login with TikTok",
    ),
    "login_with_verification_code": MessageLookupByLibrary.simpleMessage(
      "Code Login",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Logout"),
    "logout_confirm_message": MessageLookupByLibrary.simpleMessage(
      "Log out now?",
    ),
    "logout_confirm_title": MessageLookupByLibrary.simpleMessage(
      "Confirm Logout",
    ),
    "member_benefits_partner_agent_1": MessageLookupByLibrary.simpleMessage(
      "Validity Period",
    ),
    "member_benefits_partner_agent_1_value":
        MessageLookupByLibrary.simpleMessage("Permanent"),
    "member_benefits_partner_agent_2": MessageLookupByLibrary.simpleMessage(
      "Referral Bonus",
    ),
    "member_benefits_partner_agent_2_value":
        MessageLookupByLibrary.simpleMessage(
          "Receive high bonuses up to 1 billion+ upon success",
        ),
    "member_benefits_partner_agent_3": MessageLookupByLibrary.simpleMessage(
      "Team Shopping Commission",
    ),
    "member_benefits_partner_agent_3_value":
        MessageLookupByLibrary.simpleMessage(
          "Earn up to 20% cashback from each downline\'s cashback",
        ),
    "member_benefits_partner_agent_4": MessageLookupByLibrary.simpleMessage(
      "Extra Cashback",
    ),
    "member_benefits_partner_agent_4_value":
        MessageLookupByLibrary.simpleMessage("Higher cashback up to 100%"),
    "member_benefits_silver_agent_1": MessageLookupByLibrary.simpleMessage(
      "Validity Period",
    ),
    "member_benefits_silver_agent_1_value":
        MessageLookupByLibrary.simpleMessage("1 Year"),
    "member_benefits_silver_agent_2": MessageLookupByLibrary.simpleMessage(
      "Referral Bonus",
    ),
    "member_benefits_silver_agent_2_benefit":
        MessageLookupByLibrary.simpleMessage(
          "Can earn up to 10,000,000 IDR bonus",
        ),
    "member_benefits_silver_agent_2_value":
        MessageLookupByLibrary.simpleMessage(
          "Get extra rewards for successful referrals",
        ),
    "member_benefits_silver_agent_3": MessageLookupByLibrary.simpleMessage(
      "Team Shopping Commission",
    ),
    "member_benefits_silver_agent_3_benefit": MessageLookupByLibrary.simpleMessage(
      "10% commission from direct referrals\' shopping cashback (5% regular + 5% promotion)",
    ),
    "member_benefits_silver_agent_3_value":
        MessageLookupByLibrary.simpleMessage(
          "Earn commissions from your team\'s purchases",
        ),
    "member_benefits_silver_agent_4": MessageLookupByLibrary.simpleMessage(
      "Extra Cashback",
    ),
    "member_benefits_silver_agent_4_benefit": MessageLookupByLibrary.simpleMessage(
      "10% commission from direct referrals\' shopping cashback (5% regular + 5% promotion)",
    ),
    "member_benefits_silver_agent_4_value":
        MessageLookupByLibrary.simpleMessage("Higher cashback up to 50%"),
    "member_benefits_silver_agent_5": MessageLookupByLibrary.simpleMessage(
      "Unlimited Cashback",
    ),
    "member_benefits_silver_agent_5_benefit": MessageLookupByLibrary.simpleMessage(
      "10% commission from direct referrals\' shopping cashback (5% regular + 5% promotion)",
    ),
    "member_benefits_silver_agent_5_value":
        MessageLookupByLibrary.simpleMessage("Cashback without limits"),
    "member_introduction": MessageLookupByLibrary.simpleMessage(
      "Upgrade to become an agent or partner to earn more",
    ),
    "member_introduction_level_silver_agent":
        MessageLookupByLibrary.simpleMessage("Silver"),
    "member_level_partner": MessageLookupByLibrary.simpleMessage("Partner"),
    "member_level_partner_fee": MessageLookupByLibrary.simpleMessage(
      "Partner Fee",
    ),
    "member_level_silver_agent": MessageLookupByLibrary.simpleMessage(
      "Silver Agent",
    ),
    "member_level_silver_agent_fee": MessageLookupByLibrary.simpleMessage(
      "Silver Agent Fee",
    ),
    "member_level_state": MessageLookupByLibrary.simpleMessage("Level Status"),
    "member_status_description": MessageLookupByLibrary.simpleMessage(
      "You are not currently an agent or partner",
    ),
    "message_no_data": MessageLookupByLibrary.simpleMessage("No Data"),
    "modify_nickname": MessageLookupByLibrary.simpleMessage("Change Nickname"),
    "modify_password": MessageLookupByLibrary.simpleMessage("Change Password"),
    "modify_phone_number": MessageLookupByLibrary.simpleMessage("Change Phone"),
    "modify_success": MessageLookupByLibrary.simpleMessage("Updated"),
    "my_avatar": MessageLookupByLibrary.simpleMessage("My Avatar"),
    "my_collection": MessageLookupByLibrary.simpleMessage("My Collections"),
    "my_team": MessageLookupByLibrary.simpleMessage("My Team"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "name_placeholder": MessageLookupByLibrary.simpleMessage("Enter name"),
    "network_error": MessageLookupByLibrary.simpleMessage("Network error"),
    "network_is_not_available": MessageLookupByLibrary.simpleMessage(
      "Network is unavailable, please check your connection",
    ),
    "next_step": MessageLookupByLibrary.simpleMessage("Next"),
    "nickname": MessageLookupByLibrary.simpleMessage("Nickname"),
    "nickname_hint": MessageLookupByLibrary.simpleMessage(
      "Please input nickname",
    ),
    "nickname_too_long": MessageLookupByLibrary.simpleMessage(
      "Nickname is too long, maximum 10 characters",
    ),
    "no_limit": MessageLookupByLibrary.simpleMessage("Unlimited"),
    "no_limit_description": MessageLookupByLibrary.simpleMessage(
      "Enjoy unlimited cashback",
    ),
    "normal_member": MessageLookupByLibrary.simpleMessage("Regular Member"),
    "normal_member_user": MessageLookupByLibrary.simpleMessage("Normal"),
    "normal_user": MessageLookupByLibrary.simpleMessage("Regular User"),
    "normal_user_2_benefit": MessageLookupByLibrary.simpleMessage("No bonus"),
    "normal_user_3_benefit": MessageLookupByLibrary.simpleMessage(
      "No shopping commission",
    ),
    "normal_user_4_benefit": MessageLookupByLibrary.simpleMessage(
      "No shopping commission",
    ),
    "normal_user_5_benefit": MessageLookupByLibrary.simpleMessage(
      "No shopping commission",
    ),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "open_payment_link": MessageLookupByLibrary.simpleMessage(
      "Open Payment Link Directly",
    ),
    "order_application_time": MessageLookupByLibrary.simpleMessage(
      "Application Time:",
    ),
    "order_cashback_info": MessageLookupByLibrary.simpleMessage(
      "Cashback Info",
    ),
    "order_expected_cashback": MessageLookupByLibrary.simpleMessage(
      "Est. Cashback:",
    ),
    "order_payment": MessageLookupByLibrary.simpleMessage("Order Payment"),
    "order_price": MessageLookupByLibrary.simpleMessage("Order Amount"),
    "order_right_now": MessageLookupByLibrary.simpleMessage("Order Now"),
    "order_status_completed": MessageLookupByLibrary.simpleMessage("Completed"),
    "order_status_expired": MessageLookupByLibrary.simpleMessage("Expired"),
    "order_status_processing": MessageLookupByLibrary.simpleMessage(
      "Processing",
    ),
    "order_tab_all": MessageLookupByLibrary.simpleMessage("All"),
    "order_tab_completed": MessageLookupByLibrary.simpleMessage("Completed"),
    "order_tab_expired": MessageLookupByLibrary.simpleMessage("Expired"),
    "order_tab_processing": MessageLookupByLibrary.simpleMessage("Processing"),
    "order_title": MessageLookupByLibrary.simpleMessage("My Orders"),
    "partner": MessageLookupByLibrary.simpleMessage("Partner"),
    "partner_extra_bonus1": MessageLookupByLibrary.simpleMessage(
      "For every 10 Silver Partners developed: 1,000,000 IDR reward",
    ),
    "partner_extra_bonus2": MessageLookupByLibrary.simpleMessage(
      "For every 10 Gold Partners developed: 2,000,000 IDR reward",
    ),
    "partner_extra_bonus3": MessageLookupByLibrary.simpleMessage(
      "For every 10 Gold Partners developed: 2,000,000 IDR bonus;\nFor every 10 Diamond Partners developed: 100,000,000 IDR bonus.",
    ),
    "password_not_same": MessageLookupByLibrary.simpleMessage(
      "Passwords don\'t match",
    ),
    "pay_with_qrcode": MessageLookupByLibrary.simpleMessage("Pay with QR Code"),
    "pay_with_qrcode_usage": MessageLookupByLibrary.simpleMessage(
      "Scan the QR code to open the payment link and pay. If you want to pay directly in this app, tap to open the link.",
    ),
    "payment_agreement": MessageLookupByLibrary.simpleMessage("Read and Agree"),
    "payment_agreement_link": MessageLookupByLibrary.simpleMessage(
      "Payment Terms",
    ),
    "payment_amount": MessageLookupByLibrary.simpleMessage("Payment Amount"),
    "payment_complete": MessageLookupByLibrary.simpleMessage("Completed"),
    "payment_failed": MessageLookupByLibrary.simpleMessage("Payment Failed"),
    "payment_id": MessageLookupByLibrary.simpleMessage(
      "Payment Transaction ID",
    ),
    "payment_method": MessageLookupByLibrary.simpleMessage("Payment Method"),
    "payment_problem": MessageLookupByLibrary.simpleMessage("Payment Issue"),
    "payment_success": MessageLookupByLibrary.simpleMessage(
      "Payment Successful",
    ),
    "phone_number": MessageLookupByLibrary.simpleMessage("Phone"),
    "phone_number_placeholder": MessageLookupByLibrary.simpleMessage(
      "Enter Phone Number",
    ),
    "please_choose_payment_method": MessageLookupByLibrary.simpleMessage(
      "Please select a payment method",
    ),
    "please_input_amount": MessageLookupByLibrary.simpleMessage("Enter amount"),
    "please_input_bank_number": MessageLookupByLibrary.simpleMessage(
      "Enter card number",
    ),
    "please_input_e_wallet_account": MessageLookupByLibrary.simpleMessage(
      "Enter e-wallet",
    ),
    "please_input_your_password": MessageLookupByLibrary.simpleMessage(
      "Enter password",
    ),
    "please_select_bank": MessageLookupByLibrary.simpleMessage("Select bank"),
    "please_select_bank_or_e_wallet": MessageLookupByLibrary.simpleMessage(
      "Select bank/e-wallet",
    ),
    "please_select_e_wallet": MessageLookupByLibrary.simpleMessage(
      "Select e-wallet",
    ),
    "please_select_withdrawal_account": MessageLookupByLibrary.simpleMessage(
      "Select account",
    ),
    "pre_team_cashback": MessageLookupByLibrary.simpleMessage(
      "Estimated Team Contribution Cashback",
    ),
    "privacy": MessageLookupByLibrary.simpleMessage("Privacy"),
    "privacy_policy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "product_link_empty": MessageLookupByLibrary.simpleMessage(
      "No product found for the link",
    ),
    "product_link_empty_content": MessageLookupByLibrary.simpleMessage(
      "No matching product found for this link. Please check the link or try another product.",
    ),
    "product_name": MessageLookupByLibrary.simpleMessage("Product Name"),
    "purchase_right_now": MessageLookupByLibrary.simpleMessage("Pay Now"),
    "qrcode": MessageLookupByLibrary.simpleMessage("QRCode"),
    "real_payment_price": MessageLookupByLibrary.simpleMessage(
      "Actual Payment Amount",
    ),
    "rebase_cash": MessageLookupByLibrary.simpleMessage("Cashback Earned"),
    "rebase_expenditure": MessageLookupByLibrary.simpleMessage(
      "Cashback Expense",
    ),
    "rebase_income": MessageLookupByLibrary.simpleMessage("Cashback Income"),
    "rebase_info": MessageLookupByLibrary.simpleMessage("Cashback Details"),
    "rebase_price": MessageLookupByLibrary.simpleMessage("Purchase Price"),
    "rebase_rate": MessageLookupByLibrary.simpleMessage("Cashback Rate"),
    "rebate_genco_last_app_content": MessageLookupByLibrary.simpleMessage(
      "Cashback untraceable if other apps opened after GENCO.",
    ),
    "rebate_genco_last_app_title": MessageLookupByLibrary.simpleMessage(
      "Keep GENCO as Last App",
    ),
    "rebate_how_to_get_title": MessageLookupByLibrary.simpleMessage(
      "How to Earn Cashback:",
    ),
    "rebate_how_to_order_content": MessageLookupByLibrary.simpleMessage(
      "Place order via GENCO redirect to track cashback.",
    ),
    "rebate_how_to_order_title": MessageLookupByLibrary.simpleMessage(
      "How to Order",
    ),
    "rebate_instruction_content": MessageLookupByLibrary.simpleMessage(
      "Cashback credited after order confirmation & completion.",
    ),
    "rebate_instruction_title": MessageLookupByLibrary.simpleMessage(
      "Cashback Guide",
    ),
    "rebate_step_1_content": MessageLookupByLibrary.simpleMessage(
      "Order via Shopee/TikTok",
    ),
    "rebate_step_1_title": MessageLookupByLibrary.simpleMessage("01"),
    "rebate_step_2_content": MessageLookupByLibrary.simpleMessage(
      "Track status in 1-3 days",
    ),
    "rebate_step_2_title": MessageLookupByLibrary.simpleMessage("02"),
    "rebate_step_3_content": MessageLookupByLibrary.simpleMessage(
      "Get cashback after completion",
    ),
    "rebate_step_3_title": MessageLookupByLibrary.simpleMessage("03"),
    "rebate_unsupported_order_content": MessageLookupByLibrary.simpleMessage(
      "Orders not placed via GENCO redirect are ineligible.",
    ),
    "rebate_unsupported_order_title": MessageLookupByLibrary.simpleMessage(
      "Ineligible Orders",
    ),
    "received_bonus": MessageLookupByLibrary.simpleMessage("Received Bonus"),
    "resend_code": MessageLookupByLibrary.simpleMessage("Resend Code"),
    "resend_in": MessageLookupByLibrary.simpleMessage("Resend in"),
    "role": MessageLookupByLibrary.simpleMessage("Role"),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "seconds": MessageLookupByLibrary.simpleMessage("s"),
    "select_all": MessageLookupByLibrary.simpleMessage("Select All"),
    "select_bank": MessageLookupByLibrary.simpleMessage("Select Bank"),
    "select_e_wallet": MessageLookupByLibrary.simpleMessage("Select e-Wallet"),
    "set_password_hint": MessageLookupByLibrary.simpleMessage(
      "6-20 letters + numbers",
    ),
    "setting": MessageLookupByLibrary.simpleMessage("Settings"),
    "setting_login_password": MessageLookupByLibrary.simpleMessage(
      "Set Password",
    ),
    "share_text": m1,
    "shopping_bonus": MessageLookupByLibrary.simpleMessage("Shopping Bonus"),
    "silver": MessageLookupByLibrary.simpleMessage("Silver"),
    "silver_agent": MessageLookupByLibrary.simpleMessage("Silver Agent"),
    "silver_partner": MessageLookupByLibrary.simpleMessage("Silver Partner"),
    "task_banner_line1": MessageLookupByLibrary.simpleMessage(
      "The more transactions and friend",
    ),
    "task_banner_line2": MessageLookupByLibrary.simpleMessage(
      "invitations, the more",
    ),
    "task_banner_line3": MessageLookupByLibrary.simpleMessage("your earnings."),
    "task_banner_subtitle": MessageLookupByLibrary.simpleMessage(
      "Your Earnings Here!",
    ),
    "task_banner_title": MessageLookupByLibrary.simpleMessage("Check"),
    "task_cash_income": MessageLookupByLibrary.simpleMessage(
      "Cash Income (Rp)",
    ),
    "task_center": MessageLookupByLibrary.simpleMessage("Task Center"),
    "task_center_title": MessageLookupByLibrary.simpleMessage("Task Center"),
    "task_close": MessageLookupByLibrary.simpleMessage("Close"),
    "task_conditions_met": MessageLookupByLibrary.simpleMessage(
      "Conditions Met",
    ),
    "task_conditions_not_met": MessageLookupByLibrary.simpleMessage(
      "Conditions Not Met",
    ),
    "task_daily_tasks": MessageLookupByLibrary.simpleMessage("Task List"),
    "task_developing": MessageLookupByLibrary.simpleMessage(
      "Please complete invitation and first order tasks before claiming rewards",
    ),
    "task_feature_developing": MessageLookupByLibrary.simpleMessage(
      "Feature Developing",
    ),
    "task_go_claim": MessageLookupByLibrary.simpleMessage("Claim"),
    "task_invite_count": MessageLookupByLibrary.simpleMessage("Invite Count"),
    "task_invite_progress": MessageLookupByLibrary.simpleMessage(
      "Invite Progress",
    ),
    "task_invite_reward": MessageLookupByLibrary.simpleMessage(
      "Invite First Order Reward",
    ),
    "task_order_count": MessageLookupByLibrary.simpleMessage("Order Count"),
    "task_order_progress": MessageLookupByLibrary.simpleMessage(
      "Order Progress",
    ),
    "task_per_completion": MessageLookupByLibrary.simpleMessage(
      "Per Completion",
    ),
    "task_record_title": MessageLookupByLibrary.simpleMessage("Task Record"),
    "task_redeemed_invites": MessageLookupByLibrary.simpleMessage(
      "Redeemed Invites",
    ),
    "task_redeemed_orders": MessageLookupByLibrary.simpleMessage(
      "Redeemed Orders",
    ),
    "task_return_cash_welfare": MessageLookupByLibrary.simpleMessage(
      "Return Cash Welfare",
    ),
    "task_return_cash_welfare_desc": MessageLookupByLibrary.simpleMessage(
      "Daily Exclusive",
    ),
    "task_reward_amount": MessageLookupByLibrary.simpleMessage("Reward Amount"),
    "task_rules_content": MessageLookupByLibrary.simpleMessage(
      "No rules content available",
    ),
    "task_rules_title": MessageLookupByLibrary.simpleMessage("Task Rules"),
    "task_total_invites": MessageLookupByLibrary.simpleMessage("Total Invites"),
    "task_total_orders": MessageLookupByLibrary.simpleMessage("Total Orders"),
    "task_view_record": MessageLookupByLibrary.simpleMessage("View Record"),
    "task_view_rules": MessageLookupByLibrary.simpleMessage("View Rules"),
    "task_withdraw": MessageLookupByLibrary.simpleMessage("Withdraw"),
    "task_withdrawable_amount": MessageLookupByLibrary.simpleMessage(
      "Withdrawable Amount",
    ),
    "team_bonus": MessageLookupByLibrary.simpleMessage("Team Bonus"),
    "team_bonus_detail": MessageLookupByLibrary.simpleMessage(
      "Indirect referral (level 2): Each accumulates 100,000 IDR;\nIndirect referral (level 3): Each accumulates 50,000 IDR;\nIf you reach \"Gold Partner\" status (successfully refer 10 partners directly), bonus becomes withdrawable. Valid for 60 days from generation. If not achieved, bonus expires.",
    ),
    "team_purchase_bonus": MessageLookupByLibrary.simpleMessage(
      "Team Purchase Commission",
    ),
    "team_purchase_detail": MessageLookupByLibrary.simpleMessage(
      "Earn 10% commission on shopping of your directly invited team",
    ),
    "team_purchase_detail_gold": MessageLookupByLibrary.simpleMessage(
      "Indirect invitation (level 2): Each accumulates 15,000 IDR; Indirect invitation (level 3): Each accumulates 10,000 IDR.",
    ),
    "team_purchase_detail_gold2": MessageLookupByLibrary.simpleMessage(
      "Indirect invitation (level 2): Each gets 100,000 IDR; Indirect invitation (level 3): Each gets 50,000 IDR",
    ),
    "team_support": MessageLookupByLibrary.simpleMessage("Team Contribution"),
    "to_gold_progress": MessageLookupByLibrary.simpleMessage(
      "Progress to Gold Agent",
    ),
    "today": MessageLookupByLibrary.simpleMessage("Today"),
    "trade_channel": MessageLookupByLibrary.simpleMessage("Channel"),
    "trade_order_number": MessageLookupByLibrary.simpleMessage("Order ID"),
    "trade_serial_number": MessageLookupByLibrary.simpleMessage(
      "Transaction ID",
    ),
    "trade_time": MessageLookupByLibrary.simpleMessage("Time"),
    "trade_type": MessageLookupByLibrary.simpleMessage("Transaction Type"),
    "training": MessageLookupByLibrary.simpleMessage("Training"),
    "training_detail": MessageLookupByLibrary.simpleMessage(
      "Professional mentors provide high-quality courses and guidance",
    ),
    "unknown_error": MessageLookupByLibrary.simpleMessage("Unknown error"),
    "upgrade_date": MessageLookupByLibrary.simpleMessage(
      "Upgrade Date: June 11, 2025",
    ),
    "usage_guideline_description": MessageLookupByLibrary.simpleMessage(
      "Get cashback on Shopee & TikTok in 3 steps via link",
    ),
    "usage_guideline_step1": MessageLookupByLibrary.simpleMessage(
      "Open product on Shopee/TikTok and copy link",
    ),
    "usage_guideline_step2": MessageLookupByLibrary.simpleMessage(
      "Paste link in GENCO to check cashback",
    ),
    "usage_guideline_step3": MessageLookupByLibrary.simpleMessage(
      "Jump to Shopee/TikTok to complete order",
    ),
    "usage_guideline_title": MessageLookupByLibrary.simpleMessage("User Guide"),
    "usage_hint": MessageLookupByLibrary.simpleMessage("Save with GENCO"),
    "user_agreement": MessageLookupByLibrary.simpleMessage("Terms of Service"),
    "user_service": MessageLookupByLibrary.simpleMessage("Customer Service"),
    "user_service_description": MessageLookupByLibrary.simpleMessage(
      "Premium customer service",
    ),
    "valid_for": MessageLookupByLibrary.simpleMessage("Valid for: 1 year"),
    "welcome_back": MessageLookupByLibrary.simpleMessage("Welcome back!"),
    "whatsapp_account": MessageLookupByLibrary.simpleMessage("WhatsApp"),
    "whatsapp_account_hint": MessageLookupByLibrary.simpleMessage(
      "Enter WhatsApp number",
    ),
    "withdrawal_account": MessageLookupByLibrary.simpleMessage(
      "Withdrawal Account",
    ),
    "withdrawal_add_card": MessageLookupByLibrary.simpleMessage(
      "Add Bank Card",
    ),
    "withdrawal_add_e_card": MessageLookupByLibrary.simpleMessage(
      "Add e-Wallet",
    ),
    "withdrawal_all": MessageLookupByLibrary.simpleMessage("Withdraw All"),
    "withdrawal_amount": MessageLookupByLibrary.simpleMessage(
      "Withdrawal Amount",
    ),
    "withdrawal_amount_hint": MessageLookupByLibrary.simpleMessage(
      "Max withdrawable",
    ),
    "withdrawal_amount_min": MessageLookupByLibrary.simpleMessage(
      "Min withdrawable",
    ),
    "withdrawal_choose_method": MessageLookupByLibrary.simpleMessage(
      "Select Account",
    ),
    "withdrawal_failed": MessageLookupByLibrary.simpleMessage(
      "Withdrawal Failed",
    ),
    "withdrawal_fees": MessageLookupByLibrary.simpleMessage("Fee"),
    "withdrawal_fees_hint": MessageLookupByLibrary.simpleMessage(
      "1.5% fee. Min fee Rp5.550. If <Rp5.550, fee charged at Rp5.550.",
    ),
    "withdrawal_finish": MessageLookupByLibrary.simpleMessage(
      "Withdrawal Complete",
    ),
    "withdrawal_hint": MessageLookupByLibrary.simpleMessage("Note"),
    "withdrawal_hint_description": MessageLookupByLibrary.simpleMessage(
      "Funds arrive within 24h (excl. weekends/holidays)",
    ),
    "withdrawal_success": MessageLookupByLibrary.simpleMessage(
      "Withdrawal submitted",
    ),
    "withdrawal_success_hint": MessageLookupByLibrary.simpleMessage(
      "Funds arrive within 24h (excl. weekends/holidays)",
    ),
    "year": MessageLookupByLibrary.simpleMessage("Year"),
  };
}
