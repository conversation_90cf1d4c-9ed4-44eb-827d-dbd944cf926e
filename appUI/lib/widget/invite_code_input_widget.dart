import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/themes/colors.dart';

///
/// 邀请码输入框样式
///
enum InviteCodeInputItemType {
  ///
  /// 盒子
  ///
  box,
}

///
/// 邀请码输入框
///
class InviteCodeInputWidget extends StatefulWidget {
  const InviteCodeInputWidget({
    super.key,
    this.count = 6,
    this.itemWidth = 35,
    this.onSubmitted,
    this.type = InviteCodeInputItemType.box,
    this.decoration,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    required this.textStyle,
    this.focusBorderColor,
    this.borderColor,
    this.unFocus = true,
    this.autoFocus = true,
    this.showCursor = false,
    this.cursorWidth = 2,
    this.cursorColor,
    this.cursorIndent = 10,
    this.cursorEndIndent = 10,
  });

  ///
  /// 邀请码位数，默认8位
  ///
  final int count;

  ///
  /// 每一个item的宽
  ///
  final double itemWidth;

  ///
  /// 输入完成回调
  ///
  final ValueChanged<String>? onSubmitted;

  ///
  /// 每个item的装饰类型
  ///
  final InviteCodeInputItemType type;

  ///
  /// 每个item的样式
  ///
  final Decoration? decoration;

  ///
  /// 边框宽度
  ///
  final double borderWidth;

  ///
  /// 边框颜色
  ///
  final Color? borderColor;

  ///
  /// 获取焦点边框的颜色
  ///
  final Color? focusBorderColor;

  ///
  /// 边框圆角
  ///
  final double borderRadius;

  ///
  /// 文本样式
  ///
  final TextStyle textStyle;

  ///
  /// 输入完成后是否失去焦点，默认true，失去焦点后，软键盘消失
  ///
  final bool unFocus;

  ///
  /// 是否自动获取焦点
  ///
  final bool autoFocus;

  ///
  /// 是否显示光标
  ///
  final bool showCursor;

  ///
  /// 光标颜色
  ///
  final Color? cursorColor;

  ///
  /// 光标宽度
  ///
  final double cursorWidth;

  ///
  /// 光标距离顶部距离
  ///
  final double cursorIndent;

  ///
  /// 光标距离底部距离
  ///
  final double cursorEndIndent;

  @override
  State<StatefulWidget> createState() => _InviteCodeInputWidget();
}

class _InviteCodeInputWidget extends State<InviteCodeInputWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  final List<String> _contentList = [];

  @override
  void initState() {
    List.generate(widget.count, (index) {
      _contentList.add('');
    });
    _controller = TextEditingController();
    _focusNode = FocusNode();
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(_focusNode);
      },
      child: Stack(
        children: <Widget>[
          Positioned.fill(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(widget.count, (index) {
                return SizedBox(
                  width: widget.itemWidth,
                  child: InviteCodeInputItem(
                    data: _contentList[index],
                    textStyle: widget.textStyle,
                    type: widget.type,
                    decoration: widget.decoration,
                    borderRadius: widget.borderRadius,
                    borderWidth: widget.borderWidth,
                    borderColor: (_controller.text.length == index
                            ? widget.focusBorderColor
                            : widget.borderColor) ??
                        widget.borderColor,
                    showCursor:
                        widget.showCursor && _controller.text.length == index,
                    cursorColor: widget.cursorColor,
                    cursorWidth: widget.cursorWidth,
                    cursorIndent: widget.cursorIndent,
                    cursorEndIndent: widget.cursorEndIndent,
                  ),
                );
              }),
            ),
          ),
          _buildTextField(),
        ],
      ),
    );
  }

  ///
  /// 构建TextField
  ///
  TextField _buildTextField() {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      decoration: const InputDecoration(
        border: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent)),
        enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent)),
        focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent)),
      ),
      cursorWidth: 0,
      autofocus: widget.autoFocus,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')),
        UpperCaseTextFormatter(),
      ],
      maxLength: widget.count,
      buildCounter: (
        BuildContext context, {
        required int currentLength,
        int? maxLength,
        required bool isFocused,
      }) {
        return const Text('');
      },
      keyboardType: TextInputType.text,
      style: const TextStyle(color: Colors.transparent),
      onChanged: _onValueChange,
    );
  }

  void _onValueChange(String value) {
    for (int i = 0; i < widget.count; i++) {
      if (i < value.length) {
        _contentList[i] = value.substring(i, i + 1);
      } else {
        _contentList[i] = '';
      }
    }
    setState(() {});

    if (value.length == widget.count) {
      if (widget.unFocus) {
        _focusNode.unfocus();
      }
    }
    if (widget.onSubmitted != null) {
      widget.onSubmitted!(value);
    }
  }

  ///
  /// 获取当前输入的邀请码
  ///
  String get inviteCode => _controller.text;
}

///
/// 单个邀请码输入框
///
class InviteCodeInputItem extends StatelessWidget {
  const InviteCodeInputItem({
    super.key,
    this.data = '',
    required this.textStyle,
    this.type = InviteCodeInputItemType.box,
    this.decoration,
    this.borderRadius = 8.0,
    this.borderWidth = 1.0,
    this.borderColor,
    this.showCursor = false,
    this.cursorColor,
    this.cursorWidth = 2,
    this.cursorIndent = 5,
    this.cursorEndIndent = 5,
  });

  final String data;
  final InviteCodeInputItemType type;
  final double borderWidth;
  final Color? borderColor;
  final double borderRadius;
  final TextStyle textStyle;
  final Decoration? decoration;

  ///
  /// 是否显示光标
  ///
  final bool showCursor;

  ///
  /// 光标颜色
  ///
  final Color? cursorColor;

  ///
  /// 光标宽度
  ///
  final double cursorWidth;

  ///
  /// 光标距离顶部距离
  ///
  final double cursorIndent;

  ///
  /// 光标距离底部距离
  ///
  final double cursorEndIndent;

  @override
  Widget build(BuildContext context) {
    var borderColor = this.borderColor ?? const Color(0xFFFAF3F2);
    var text = _buildText();
    Widget mWidget = _buildBoxDecoration(text, borderColor);

    return Stack(
      children: <Widget>[
        mWidget,
        showCursor
            ? Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(borderRadius),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black12,
                      offset: Offset(0.0, 1.0),
                      blurRadius: 10.0,
                      spreadRadius: 0,
                    )
                  ],
                ),
              )
            : Container()
      ],
    );
  }

  ///
  /// 绘制盒子类型
  ///
  Container _buildBoxDecoration(Widget child, Color borderColor) {
    return Container(
      width: 35,
      height: 40,
      alignment: Alignment.center,
      decoration: decoration ??
          BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color: data.isNotEmpty ? const Color(0xFFE6AC44) : borderColor,
              width: borderWidth,
            ),
            boxShadow: data.isEmpty
                ? [
                    const BoxShadow(
                      color: Colors.black12,
                      offset: Offset(0, 1),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ]
                : [
                    const BoxShadow(
                      color: Colors.black12,
                      offset: Offset(0.0, 1.0),
                      blurRadius: 6.0,
                      spreadRadius: 0,
                    )
                  ],
          ),
      child: child,
    );
  }

  ///
  /// 文本
  ///
  Text _buildText() {
    return Text(
      data,
      style: textStyle,
    );
  }
}

// 自定义TextInputFormatter，将输入的字母转换为大写
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}