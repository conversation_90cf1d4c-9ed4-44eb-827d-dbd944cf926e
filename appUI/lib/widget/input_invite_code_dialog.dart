import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/models/bind_invite_code_response.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/widget/loading_dialog.dart';

import '../generated/l10n.dart';
import '../network/errors.dart';
import '../r.dart';
import 'invite_group_information_bottom_view.dart';
import 'invite_code_input_widget.dart';

class BottomInputInviteCodeView extends StatefulWidget {
  final VoidCallback? onButtonTapped;

  const BottomInputInviteCodeView({
    super.key,
    this.onButtonTapped,
  });

  @override
  State<BottomInputInviteCodeView> createState() => _BottomInputInviteCodeViewState();
}

class _BottomInputInviteCodeViewState extends State<BottomInputInviteCodeView> {
  String _inviteCode = '';

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Stack(
        children: [
          Image.asset(
            R.assetsImagesBgCashInfo,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width,
          ),
          buildContent(context),
        ],
      ),
    );
  }

  Widget buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 22),
        Text(
          S.of(context).input_invite_code,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 15,
            color: primaryTextColor,
          ),
        ),
        SizedBox(height: 16),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              InviteCodeInputWidget(
                 count: 6,
                 itemWidth: 35,
                textStyle: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: primaryTextColor,
                ),
                borderColor: Colors.grey[300],
                focusBorderColor: Color(0xFFE6AC44),
                onSubmitted: (value) {
                  setState(() {
                    _inviteCode = value;
                  });
                },
              ),
            ],
          ),
        ),
        SizedBox(height: 16),
        InkWell(
          onTap: () async {
            String inviteCode = _inviteCode.trim();
            if(inviteCode.isEmpty) {
              makeToast(S.of(context).invite_code_empty_hint);
              return;
            }
            try {
              showLoadingDialog();
              BindInviteCodeResponse bindInviteCodeResponse = await networkApiClient.bindInviteCode(inviteCode: inviteCode);
              dismissLoadingDialog();
              if(widget.onButtonTapped != null) {
                widget.onButtonTapped!();
              }
              if(context.mounted) {
                Navigator.of(context).pop(context);
                showInviteGroupInformationBottomView(context, bindInviteCodeResponse: bindInviteCodeResponse);
              }
            } catch (e) {
              dismissLoadingDialog();
              if(context.mounted) {
                ApiErrorHandler.handleError(e, context);
              }
            }
          },
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 16),
            height: 44,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFFFE9C7), Color(0xFFFFE9C7)],
              ),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Center(
              child: Text(
                S.of(context).next_step,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
      ],
    );
  }
}

void showBottomInputInviteCodeView(
    BuildContext context, {
      VoidCallback? onButtonTapped,
    }) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return BottomInputInviteCodeView(onButtonTapped: onButtonTapped);
    },
  );
}