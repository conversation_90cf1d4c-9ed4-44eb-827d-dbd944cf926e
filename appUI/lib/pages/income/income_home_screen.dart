import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/main.dart';
import 'package:milestone/pages/income/amount_to_be_credited_screen.dart';
import 'package:milestone/pages/income/transaction_details_screen.dart';


import 'package:milestone/pages/member/member_level_status_screen.dart';
import 'package:milestone/pages/orders/my_order_screen.dart';
import 'package:milestone/pages/withdrawal/withdrawal_screen.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/navigation_route.dart';
import 'package:milestone/utils/string.dart';

import 'package:milestone/widget/common_dialog.dart';
import 'package:milestone/widget/image_widget.dart';
import 'package:milestone/widget/member_level_badge_view.dart';

import '../../controller/network_refresh_mixin.dart';
import '../../controller/refresh_listener_mixin.dart';
import '../../models/revenue_info.dart';
import '../../network/errors.dart';
import '../../network/network_api_client.dart';

import '../../utils/profile_listener_mixin.dart';
import 'income_detail_screen.dart';

class IncomeHomeScreen extends StatefulWidget {
  const IncomeHomeScreen({super.key});

  @override
  State<StatefulWidget> createState() => _IncomeHomeScreenState();
}

class _IncomeHomeScreenState extends State<IncomeHomeScreen>
    with AutomaticKeepAliveClientMixin, ProfileListenerMixin, NetworkRefreshMixin, RefreshListenerMixin, WidgetsBindingObserver {
  RevenueInfo revenueInfo = RevenueInfo(
    estimatedTotalAmount: '--',
    pendingAmount: '--',
    receivedAmount: '--',
    withdrawableAmount: '--',
    estimatedTodayAmount: '--',
  );

  bool _isFirstLoad = true;

  @override
  void initState() {
    if (Platform.isAndroid) {
      SystemUiOverlayStyle style = const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      );
      SystemChrome.setSystemUIOverlayStyle(style);
    }
    WidgetsBinding.instance.addObserver(this);
    startLoading();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次页面变为可见时刷新数据（除了第一次加载）
    if (!_isFirstLoad) {
      Logger().d("Income page became visible - refreshing data");
      startLoading();
    }
    _isFirstLoad = false;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用从后台回到前台时，刷新数据
    if (state == AppLifecycleState.resumed) {
      Logger().d("App resumed - refreshing income data");
      startLoading();
    }
  }

  Future<void> startLoading() async {
    try {
      RevenueInfo result = await networkApiClient.getRevenueInfo();
      Logger().d("response: $result");
      if (mounted) {
        setState(() {
          revenueInfo = result;
        });
      }
    } catch (e) {
      if (mounted) {
        ApiErrorHandler.handleError(e, context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(body: SingleChildScrollView(child: buildContent(context)));
  }

  Widget buildContent(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          Assets.imagesBgCommon,
          fit: BoxFit.cover,
          width: MediaQuery.of(context).size.width,
        ),

        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildHeaderContent(context),
            SizedBox(height: 12),
            InkWell(
              onTap: () {
                showCustomDialog(
                  context: context,
                  title: S.of(context).income_pre_total_income,
                  description: S
                      .of(context)
                      .income_pre_total_income_description,
                  confirmButtonText: S.of(context).ok,
                  onConfirm: (BuildContext dialogContext) {
                    Navigator.pop(dialogContext);
                  },
                );
              },
              child: buildTotalIncomeView(context),
            ),
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    customRouter(
                      context,
                      AmountToBeCreditedScreen(revenueInfo: revenueInfo),
                    );
                  },
                  child: buildAmountToBeCreditedView(context),
                ),
                GestureDetector(
                  onTap: () {
                    showCustomDialog(
                      context: context,
                      title: S.of(context).income_amount_credited,
                      description: S
                          .of(context)
                          .income_amount_credited_description,
                      confirmButtonText: S.of(context).ok,
                      onConfirm: (BuildContext dialogContext) {
                        Navigator.pop(dialogContext);
                      },
                    );
                  },
                  child: buildAmountCreditedView(context),
                ),
              ],
            ),
            buildAmountAvailableForWithdrawalView(context),
            buildMyOrderView(context),
            buildIncomeDetailView(context),
          ],
        ),
      ],
    );
  }

  Widget buildHeaderContent(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 12,
        left: 12,
        right: 12,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipOval(
            child: ImageWidget(
              width: 52,
              height: 52,
              url: avatarUrl,
              defaultImagePath: Assets.imagesIcAvatarDefault,
              loadingWidth: 52,
              loadingHeight: 52,
            ),
          ),
          SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                nickname,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: primaryTextColor,
                ),
              ),
              SizedBox(height: 4),
              InkWell(
                onTap: () {
                  if(!Global.isDebugMode) {
                    // 统一跳转到会员等级状态界面
                    customRouter(context, MemberLevelStatusScreen(memberLevel: memberLevel));
                  }
                },
                child: MemberLevelBadgeView(memberLevel: memberLevelValue)
              ),
            ],
          ),

          SizedBox(height: 20),
          // Add more widgets here as needed
        ],
      ),
    );
  }

  Widget buildTotalIncomeView(BuildContext context) {
    return Container(
      margin: EdgeInsets.zero,
      child: Stack(
        children: [
          Image.asset(
            R.assetsImagesBgIncomeTotoalIncome,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width,
          ),
          Positioned(
            bottom: 36,
            left: 12,
            right: 12,
            child: Padding(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        S.of(context).income_pre_total_income,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: primaryTextColor,
                        ),
                      ),
                      SizedBox(width: 4),
                      Image.asset(R.assetsImagesIcDetailQuestion),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: "Rp",
                          style: const TextStyle(
                            color: highlightTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            fontFamily: "Gilroy",
                          ),
                        ),
                        TextSpan(
                          text: revenueInfo.estimatedTotalAmount.formatIDR(),
                          style: const TextStyle(
                            color: highlightTextColor,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            fontFamily: "Gilroy",
                          ),
                        ),
                      ],
                    ),
                    maxLines: 1,
                  ),
                  SizedBox(height: 8),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: S.of(context).income_today,
                          style: const TextStyle(
                            color: unSelectedTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        TextSpan(
                          text: "+Rp${revenueInfo.estimatedTodayAmount.formatIDR()}",
                          style: const TextStyle(
                            color: primaryTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildAmountToBeCreditedView(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width / 2 - 13 * 2,
      padding: EdgeInsets.all(21),
      margin: EdgeInsets.only(top: 0, left: 12, right: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                S.of(context).income_amount_to_be_credited,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: unSelectedTextColor,
                ),
              ),
              SizedBox(width: 4),
              Image.asset(R.assetsImagesIcDetailArrowRight),
            ],
          ),
          SizedBox(height: 8),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: "Rp",
                  style: const TextStyle(
                    color: primaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    fontFamily: "Gilroy",
                  ),
                ),
                TextSpan(
                  text: revenueInfo.pendingAmount.formatIDR(),
                  style: const TextStyle(
                    color: primaryTextColor,
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    fontFamily: "Gilroy",
                  ),
                ),
              ],
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  Widget buildAmountCreditedView(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width / 2 - 12 * 2,
      padding: EdgeInsets.all(21),
      margin: EdgeInsets.only(top: 0, left: 14, right: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                S.of(context).income_amount_credited,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: unSelectedTextColor,
                ),
              ),
              SizedBox(width: 4),
              Image.asset(R.assetsImagesIcDetailQuestion),
            ],
          ),
          SizedBox(height: 8),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: "Rp",
                  style: const TextStyle(
                    color: primaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    fontFamily: "Gilroy",
                  ),
                ),
                TextSpan(
                  text: revenueInfo.receivedAmount.formatIDR(),
                  style: const TextStyle(
                    color: primaryTextColor,
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    fontFamily: "Gilroy",
                  ),
                ),
              ],
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  Widget buildAmountAvailableForWithdrawalView(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width - 12 * 2,
      padding: EdgeInsets.all(21),
      margin: EdgeInsets.only(top: 20, left: 12, right: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              customRouter(context, TransactionDetailsScreen());
            },
            child: Row(
              children: [
                Text(
                  S.of(context).income_amount_available_for_withdrawal,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: unSelectedTextColor,
                  ),
                ),
                SizedBox(width: 4),
                Image.asset(R.assetsImagesIcDetailQuestion),
                Spacer(),
                Text(
                  S.of(context).income_transaction_detail,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: unSelectedTextColor,
                  ),
                ),
                Image.asset(R.assetsImagesIcDetailArrowRight),
              ],
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: "Rp",
                      style: const TextStyle(
                        color: primaryTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        fontFamily: "Gilroy",
                      ),
                    ),
                    TextSpan(
                      text: revenueInfo.withdrawableAmount.formatIDR(),
                      style: const TextStyle(
                        color: primaryTextColor,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        fontFamily: "Gilroy",
                      ),
                    ),
                  ],
                ),
                maxLines: 1,
              ),
              Spacer(),
              GestureDetector(
                onTap: () {
                  customRouter(
                    context,
                    WithdrawalScreen(revenueInfo: revenueInfo),
                  );
                },
                child: buildWithdrawalButton(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildWithdrawalButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFC80D1F), Color(0xFFFB4143)],
        ),
      ),
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Row(
        children: [
          Image.asset(Assets.imagesIcIncomeWithdrawal),
          SizedBox(width: 4),
          Text(
            S.of(context).income_withdrawal_button,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildMyOrderView(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(
          context,
        ).push(MaterialPageRoute(builder: (context) => MyOrderScreen()));
      },
      child: Container(
        width: MediaQuery.of(context).size.width - 12 * 2,
        padding: EdgeInsets.all(21),
        margin: EdgeInsets.only(top: 20, left: 12, right: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(Assets.imagesIcIncomeMyOrder),
            SizedBox(width: 12),
            Text(
              S.of(context).income_my_order,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: primaryTextColor,
              ),
            ),
            Spacer(),
            Image.asset(Assets.imagesIcIncomeArrowRight),
          ],
        ),
      ),
    );
  }

  Widget buildIncomeDetailView(BuildContext context) {
    return GestureDetector(
      onTap: () {
        customRouter(context, IncomeDetailScreen(revenueInfo: revenueInfo));
      },
      child: Container(
        width: MediaQuery.of(context).size.width - 12 * 2,
        padding: EdgeInsets.all(21),
        margin: EdgeInsets.only(top: 20, left: 12, right: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              children: [
                Image.asset(Assets.imagesIcIncomeDetail),
                SizedBox(width: 12),
                Text(
                  S.of(context).income_income_detail,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: primaryTextColor,
                  ),
                ),
                Spacer(),
                Image.asset(Assets.imagesIcIncomeArrowRight),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  @override
  bool shouldRefreshForTabIndex(int tabIndex) {
    // 只有当点击收入页面（index == 2）时才刷新
    return tabIndex == 2;
  }

  @override
  Future<void> homeTabDoubleTapRefresh() async {
    startLoading();
  }
}
