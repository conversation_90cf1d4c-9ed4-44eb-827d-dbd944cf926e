import 'package:flutter/material.dart';
import 'package:milestone/pages/me/step_list_view.dart';
import 'package:milestone/pages/member/member_single_level.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';

import '../../generated/assets.dart';
import '../../generated/l10n.dart';
import '../../r.dart';
import '../../utils/navigation_route.dart';
import '../member/member_level.dart';
import '../share/share_poster_view.dart';

class LevelUpDescriptionView extends StatelessWidget {
  late TableModel tableModel;
  final MemberSingleLevel memberSingleLevel;

  LevelUpDescriptionView({super.key, required this.memberSingleLevel});

  @override
  Widget build(BuildContext context) {
    tableModel = TableModel(
      headers: [S.of(context).role, S.of(context).bonus],
      rows: [
        [
          S.of(context).direct_invite_reward,
          S.of(context).direct_invite_detail3,
        ],
        [
          S.of(context).team_bonus,
          S.of(context).team_bonus_detail,
        ],
        [
          S.of(context).extra_bonus,
          S.of(context).partner_extra_bonus3,
        ],
      ],
    );

    return BackgroundScaffold(
      title: S.of(context).level_up_description,
      backgroundImage: R.assetsImagesBgLevelUpDescription,
      child: SingleChildScrollView(
        child: Stack(
          children: [
            buildContentView(context),
            Positioned(right: 0, child: Image.asset(R.assetsImagesIcMissionCenter))
          ],
        ),
      )
    );
  }
  
  Widget buildContentView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "10.000.000",
                style: TextStyle(
                  color: Color(0xFFC80D1F),
                  fontSize: 34,
                  fontWeight: FontWeight.w800,
                  fontStyle: FontStyle.italic,
                ),
              ),
              Text(
                S.of(context).level_up_description_title,
                style: TextStyle(
                    color: primaryTextColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600
                ),
              ),
              SizedBox(height: 12),
              Text(
                S.of(context).level_up_description_title1,
                style: TextStyle(
                    color: primaryTextColor,
                    fontSize: 13,
                    fontWeight: FontWeight.w400
                ),
              ),
            ],
          )
        ),
        SizedBox(height: 20),
        Container(
          margin: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16)
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Image.asset(R.assetsImagesIcBrandHomeStar),
                  Text(
                    S.of(context).level_up_bonus,
                    style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 15,
                        fontWeight: FontWeight.w600
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12),
              Container(
                  height: 40,
                  decoration: BoxDecoration(
                      color: Color(0xFFFFF0E3),
                      borderRadius: BorderRadius.circular(20)
                  ),
                  child: Center(
                    child: Text(
                      S.of(context).invite_agent,
                      style: TextStyle(
                          color: primaryTextColor,
                          fontSize: 15,
                          fontWeight: FontWeight.w600
                      ),
                    ),
                  )
              ),
              SizedBox(height: 12),
              StepListView(total: 10, step: 1, stepText: "Rp35.000", targetText: S.of(context).gold_agent, ratio: 1),

              SizedBox(height: 12),
              Container(
                  height: 40,
                  decoration: BoxDecoration(
                      color: Color(0xFFFFF0E3),
                      borderRadius: BorderRadius.circular(20)
                  ),
                  child: Center(
                    child: Text(
                      S.of(context).invite_gold_agent,
                      style: TextStyle(
                          color: primaryTextColor,
                          fontSize: 15,
                          fontWeight: FontWeight.w600
                      ),
                    ),
                  )
              ),
              SizedBox(height: 12),
              StepListView(total: 10, step: 1, stepText: "Rp330.000", targetText: S.of(context).diamond_agent, ratio: 10),

              SizedBox(height: 12),
              Container(
                  height: 40,
                  decoration: BoxDecoration(
                      color: Color(0xFFFFF0E3),
                      borderRadius: BorderRadius.circular(20)
                  ),
                  child: Center(
                    child: Text(
                      S.of(context).invite_diamond_agent,
                      style: TextStyle(
                          color: primaryTextColor,
                          fontSize: 15,
                          fontWeight: FontWeight.w600
                      ),
                    ),
                  )
              ),
              SizedBox(height: 12),
              StepListView(total: 10, step: 1, stepText: "Rp1.000.000", targetText: S.of(context).partner, ratio: 10),
            ],
          ),
        ),

        Container(
          margin: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16)
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Image.asset(R.assetsImagesIcBrandHomeStar),
                  Text(
                    S.of(context).activity_rule,
                    style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 15,
                        fontWeight: FontWeight.w600
                    ),
                  ),
                ],
              ),

              Container(
                  margin: EdgeInsets.only(top: 15, left: 0, right: 0, bottom: 15),
                  padding: EdgeInsets.all(2),
                  decoration: BoxDecoration(
                      color:Color(0xFFFFF9F4),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(color: Color(0xFFFFF9F4), width: 1)
                  ),
                  child: _buildFixedHeightTable(context)
              ),

              buildInviteButton(context)
            ],
          ),
        )
      ],
    );
  }

  Widget buildInviteButton(BuildContext context) {
    return InkWell(
      onTap: () {
        customRouter(context, SharePosterView());
      },
      child: Container(
        height: 46,
        margin: EdgeInsets.only(
          right: 16,
          left: 16,
          top: 20,
          bottom: 8 + MediaQuery.of(context).padding.bottom,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFE6AC44), Color(0xFFE5B670)],
          ),
          borderRadius: BorderRadius.circular(30),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(Assets.imagesIcGroupInvite),
              SizedBox(width: 4),
              Text(
                S.of(context).invite_and_earn_money,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFixedHeightTable(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        minHeight: (tableModel.rows.length + 1)*48,
        maxHeight: (tableModel.rows.length + 1)*100,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 表头
          _buildTableHeader(),
          // 表格内容 - 使用自定义分隔线
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: ClampingScrollPhysics(),
              itemCount: tableModel.rows.length,
              itemBuilder: (context, index) =>
                  _buildTableRowWithDivider(index, tableModel.rows[index], index == tableModel.rows.length - 1),
            ),
          ),
        ],
      ),
    );
  }

  // 表头构建方法保持不变
  Widget _buildTableHeader() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      decoration: BoxDecoration(
        color: Color(0xFFFEF4E0),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: List.generate(tableModel.headers.length, (colIndex) {
          return Expanded(
            flex: colIndex == 0 ? 1 : 2,
            child: Text(
              tableModel.headers[colIndex],
              textAlign: TextAlign.center,
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          );
        }),
      ),
    );
  }

// 带分割线的表格行
  Widget _buildTableRowWithDivider(int index, List<dynamic> rowData, bool isLastRow) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          child: Row(
            children: List.generate(rowData.length, (colIndex) {
              return Expanded(
                flex: colIndex == 0 ? 1 : 2,
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    '${rowData[colIndex]}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: colIndex == 0 ? primaryTextColor : secondaryTextColor,
                        fontSize: 12,
                        fontWeight: colIndex == 0 ? FontWeight.w700 : FontWeight.w400
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
        // 最后一行不加分隔线
        if (!isLastRow) Divider(
          height: 1,
          thickness: 1,
          color: Color(0xFFFDEBD3), // 使用更浅的分隔线颜色
          indent: 0, // 左侧缩进
          endIndent: 0, // 右侧缩进
        ),
      ],
    );
  }
}
