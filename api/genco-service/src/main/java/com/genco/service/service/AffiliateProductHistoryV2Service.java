package com.genco.service.service;

import com.genco.common.model.affiliate.AffiliateProductHistoryV2;
import com.baomidou.mybatisplus.extension.service.IService;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.CreatorSelectAffiliateProductResponseDataProducts;

import java.util.List;

/**
 * 联盟商品历史记录服务接口 V2
 */
public interface AffiliateProductHistoryV2Service extends IService<AffiliateProductHistoryV2> {

    /**
     * 同步TikTok商品数据到历史记录表
     * @param products TikTok商品列表
     */
    void syncTikTokProducts(List<CreatorSelectAffiliateProductResponseDataProducts> products);

    /**
     * 根据商品ID查询历史记录
     * @param productId 商品ID
     * @return 历史记录
     */
    AffiliateProductHistoryV2 getByProductId(String productId);

    /**
     * 批量根据商品ID查询历史记录
     * @param productIds 商品ID列表
     * @return 历史记录列表
     */
    List<AffiliateProductHistoryV2> getByProductIds(List<String> productIds);
}