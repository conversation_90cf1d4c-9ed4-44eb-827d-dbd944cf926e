package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.model.affiliate.AffiliateProductHistoryV2;
import com.genco.service.dao.AffiliateProductHistoryV2Dao;
import com.genco.service.service.AffiliateProductHistoryV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.CreatorSelectAffiliateProductResponseDataProducts;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 联盟商品历史记录服务实现类 V2
 */
@Slf4j
@Service
public class AffiliateProductHistoryV2ServiceImpl
        extends ServiceImpl<AffiliateProductHistoryV2Dao, AffiliateProductHistoryV2>
        implements AffiliateProductHistoryV2Service {



    @Override
    public void syncTikTokProducts(List<CreatorSelectAffiliateProductResponseDataProducts> products) {
        if (products == null || products.isEmpty()) {
            return;
        }

        try {
            List<AffiliateProductHistoryV2> historyList = products.stream()
                    .map(this::convertToHistory)
                    .collect(Collectors.toList());

            // 获取所有产品ID
            List<String> productIds = historyList.stream()
                    .map(AffiliateProductHistoryV2::getProductId)
                    .collect(Collectors.toList());

            // 查询已存在的记录
            List<AffiliateProductHistoryV2> existingRecords = this.getByProductIds(productIds);
            
            // 创建已存在产品ID的集合，用于快速查找
            List<String> existingProductIds = existingRecords.stream()
                    .map(AffiliateProductHistoryV2::getProductId)
                    .collect(Collectors.toList());

            // 分离新增和更新的记录
            List<AffiliateProductHistoryV2> newRecords = new ArrayList<>();
            List<AffiliateProductHistoryV2> updateRecords = new ArrayList<>();

            for (AffiliateProductHistoryV2 history : historyList) {
                if (existingProductIds.contains(history.getProductId())) {
                    // 找到对应的已存在记录，设置ID和创建时间
                    AffiliateProductHistoryV2 existingRecord = existingRecords.stream()
                            .filter(r -> r.getProductId().equals(history.getProductId()))
                            .findFirst()
                            .orElse(null);
                    if (existingRecord != null) {
                        history.setId(existingRecord.getId());
                        history.setCreateTime(existingRecord.getCreateTime());
                        history.setUpdateTime(new Date());
                        updateRecords.add(history);
                    }
                } else {
                    newRecords.add(history);
                }
            }

            // 批量插入新记录
            if (!newRecords.isEmpty()) {
                this.saveBatch(newRecords);
                log.info("成功插入{}个新商品到历史记录表V2", newRecords.size());
            }

            // 批量更新已存在记录
            if (!updateRecords.isEmpty()) {
                this.updateBatchById(updateRecords);
                log.info("成功更新{}个已存在商品到历史记录表V2", updateRecords.size());
            }

            log.info("成功同步{}个商品到历史记录表V2（新增：{}，更新：{}）", 
                    historyList.size(), newRecords.size(), updateRecords.size());
        } catch (Exception e) {
            log.error("同步商品到历史记录表V2失败", e);
            throw new RuntimeException("同步商品历史记录失败", e);
        }
    }

    @Override
    public AffiliateProductHistoryV2 getByProductId(String productId) {
        LambdaQueryWrapper<AffiliateProductHistoryV2> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AffiliateProductHistoryV2::getProductId, productId)
                .orderByDesc(AffiliateProductHistoryV2::getCreateTime)
                .last("LIMIT 1");
        return this.getOne(wrapper);
    }

    @Override
    public List<AffiliateProductHistoryV2> getByProductIds(List<String> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AffiliateProductHistoryV2> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AffiliateProductHistoryV2::getProductId, productIds)
                .orderByDesc(AffiliateProductHistoryV2::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 转换TikTok商品数据为历史记录
     */
    private AffiliateProductHistoryV2 convertToHistory(CreatorSelectAffiliateProductResponseDataProducts product) {
        AffiliateProductHistoryV2 history = new AffiliateProductHistoryV2();

        // 基础信息
        history.setProductId(product.getId());
        history.setTitle(product.getTitle());
        history.setMainImageUrl(product.getMainImageUrl());
        history.setBrandName(product.getBrandName());

        // 价格信息
        if (product.getPrice() != null) {
            if (product.getPrice().getFloorPrice() != null) {
                history.setFloorPrice(product.getPrice().getFloorPrice());
            }
            if (product.getPrice().getCeilingPrice() != null) {
                history.setCeilingPrice(product.getPrice().getCeilingPrice());
            }
            history.setCurrencyName(product.getPrice().getCurrency());
        }

        // 佣金信息
        if (product.getCommission() != null) {
            if (product.getCommission().getRate() != null) {
                history.setCommissionRate(product.getCommission().getRate());
            }
            if (product.getCommission().getAmount() != null) {
                history.setCommissionAmount(product.getCommission().getAmount());
            }
        }

        // 评价信息
        if (product.getReview() != null) {
            if (product.getReview().getCount() != null) {
                history.setReviewCount(String.valueOf(product.getReview().getCount()));
            }
            if (product.getReview().getOverallScore() != null) {
                history.setReviewOverallScore(product.getReview().getOverallScore());
            }
        }

        // 店铺信息
        if (product.getShop() != null) {
            history.setShopName(product.getShop().getName());
            history.setShopLogoUrl(product.getShop().getLogoUrl());
            history.setShopRating(product.getShop().getRating());
        }

        // 库存信息
        if (product.getStock() !=  null) {
            history.setStockQuantity(product.getStock().getQuantity());
        }
        if (product.getMarketPerformance() != null) {
            history.setHistoricalSoldQuantity(product.getMarketPerformance().getHistoricalSoldQuantity()); // 默认有库存，实际需要根据API响应调整
        }

        // V2版本新增字段
        history.setOperationType("SEARCH");
        history.setStatus(AffiliateProductHistoryV2.STATUS_PENDING); // 默认待处理状态
        history.setCreateTime(new Date());
        history.setUpdateTime(new Date());

        return history;
    }
}