package com.genco.admin.controller;

import com.genco.common.request.AffiliateProductSearchRequestV2;
import com.genco.common.response.AffiliateProductResponseV2;
import com.genco.common.response.CommonResult;
import com.genco.service.service.AffiliateProductServiceV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tiktokshop.open.sdk_java.invoke.ApiException;

/**
 * 联盟选品控制器 V2
 */
@Slf4j
@RestController
@RequestMapping("api/v2/admin/affiliate/products")
@Api(tags = "联盟选品管理V2")
public class AffiliateProductV2Controller {

    @Autowired
    private AffiliateProductServiceV2 affiliateProductServiceV2;

    /**
     * 搜索联盟产品 V2
     */
    @ApiOperation("搜索联盟产品V2")
    @PostMapping("/search")
    public CommonResult<AffiliateProductResponseV2> searchProducts(
            @Validated @RequestBody AffiliateProductSearchRequestV2 request) {

        log.info("开始搜索联盟产品V2，请求参数: {}", request);

        try {
            AffiliateProductResponseV2 response = affiliateProductServiceV2.searchProducts(request);
            log.info("联盟产品搜索V2成功，返回{}个产品",
                    response.getProducts() != null ? response.getProducts().size() : 0);
            return CommonResult.success(response);

        } catch (ApiException e) {
            log.error("联盟产品搜索V2失败，API异常: {}", e.getMessage(), e);

            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                // 根据不同的错误类型返回友好的错误信息
                if (errorMessage.contains("400") || errorMessage.contains("Bad Request")) {
                    return CommonResult.failed("请求参数错误，请检查搜索条件");
                } else if (errorMessage.contains("401") || errorMessage.contains("Unauthorized")) {
                    return CommonResult.failed("TikTok API认证失败，请检查访问令牌");
                } else if (errorMessage.contains("403") || errorMessage.contains("Forbidden")) {
                    return CommonResult.failed("TikTok API权限不足，请检查应用权限配置");
                } else if (errorMessage.contains("429") || errorMessage.contains("rate limit")) {
                    return CommonResult.failed("TikTok API调用频率超限，请稍后重试");
                } else if (errorMessage.contains("配置")) {
                    return CommonResult.failed("TikTok API配置错误: " + errorMessage);
                }
            }

            return CommonResult.failed("联盟产品搜索V2失败: " + errorMessage);

        } catch (Exception e) {
            log.error("联盟产品搜索V2失败，系统异常: {}", e.getMessage(), e);
            return CommonResult.failed("系统异常，请稍后重试");
        }
    }

    /**
     * 获取API版本信息
     */
    @ApiOperation("获取API版本信息")
    @GetMapping("/version")
    public CommonResult<String> getVersion() {
        return CommonResult.success("V2 - 基于TikTok AffiliateCreatorV202501Api");
    }
}
