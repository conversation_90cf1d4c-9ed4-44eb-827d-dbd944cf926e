package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 联盟选品搜索请求对象 V2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AffiliateProductSearchRequestV2", description = "联盟选品搜索请求对象 V2")
public class AffiliateProductSearchRequestV2 implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "The value of \"page_size\" must be greater than 0 and less than or equal to 50.")
    @Min(value = 1, message = "每页数量不能小于1")
    @Max(value = 50, message = "每页数量不能大于50")
    private Long pageSize = 20L;

    @ApiModelProperty(value = "Pagination offset determines where you begin to search for. It's empty when raise your first request.")
    private String pageToken;

    @ApiModelProperty(value = "The exact product IDs the search needs. If this field is not empty, we will ignore other fields\n" +
            "Prereqsites:\n" +
            "The length of product_ids should be less or equal than 50")
    private List<String> productIds;

    @ApiModelProperty(value = "The keyword of product name, which will be used for fuzzy search on products.\n" +
            "There is no limit to the language of product name\n" +
            "Prerequisites:\n" +
            "- A keyword must have at least 1 character and no more than 255 characters.")
    private String titleKeyword;

    @ApiModelProperty(value = "The categories' unique IDs of the searched product needed to be limited in this param\n" +
            "Prerequisites:\n" +
            "- The length of category_ids is less than 1000")
    private List<String> categoryIds;

    @ApiModelProperty(value = "The minimum value of commission rate in the search scope\n" +
            "Prerequisites:\n" +
            "- The value of must be greater than 100 or equal to 100, and less than 8000 or equal to 8000.\n" +
            "- This value equals the actual commission rate multiplied by 100. For example: 1200 means the actual commission rate is 12%\n" +
            "- Currently, the value will be automatically truncated to its floor hundred. For instance, 1239 will be truncated to 1200")
    private Integer commissionRateGe;

    @ApiModelProperty(value = "The maximum value of commission rate in the search range\n" +
            "Prerequisites:\n" +
            "- The value of must be greater than 100 or equal to 100, and less than 8000 or equal to 8000.\n" +
            "- This value equals the actual commission rate multiplied by 100. For example: 1200 means the actual commission rate is 12%\n" +
            "- Currently, the value will be automatically truncated to its floor hundred. For instance, 1239 will be truncated to 1200")
    private Integer commissionRateLe;

    @ApiModelProperty(value = "The minimum price of the searched product needed to be limited\n" +
            "The unit is the local currency of the creator's marketing country.\n" +
            "Prerequisites:\n" +
            "- The value must be greater than or equal to zero\n" +
            "- If no value is given, it means 0\n" +
            "- Currently, the value needs to be an integer, otherwise we will automatically truncate it to its floor integer. For instance, 12.54 will be truncated to 12")
    private String priceRangeGe;

    @ApiModelProperty(value = "The maximum price of the searched product needed to be limited\n" +
            "The unit is the local currency of the creator's marketing country.\n" +
            "Prerequisites:\n" +
            "- The value must be greater than or equal to zero\n" +
            "- If no value is given, it means 0\n" +
            "- Currently, the value needs to be an integer, otherwise we will automatically truncate it to its floor integer. For instance, 12.54 will be truncated to 12")
    private String priceRangeLe;

    @ApiModelProperty(value = "The minimum value of shop rating for the search.\n" +
            "Prerequisites:\n" +
            "- The value of must be greater than or equal to 0, and less than 50 or equal to 50.\n" +
            "- This value equals the actual shop rating multiplied by 10. For example, 35 means the actual shop rating is 3.5")
    private Integer shopRatingGe;

    @ApiModelProperty(value = "The maximum value of shop rating for the search.\n" +
            "Prerequisites:\n" +
            "- The value of must be greater than or equal to 0, and less than 50 or equal to 50.\n" +
            "- This value equals the actual shop rating multiplied by 10. For example, 35 means the actual shop rating is 3.5")
    private Integer shopRatingLe;

    @ApiModelProperty(value = "The minimum value of product sold quantity.\n" +
            "Prerequisites:\n" +
            "- The value must be greater than or equal to 0\n" +
            "- If no value is given, it means 0")
    private Integer soldQuantityGe;

    @ApiModelProperty(value = "The maximum value of product sold quantity.\n" +
            "Prerequisites:\n" +
            "- The value must be greater than or equal to 0\n" +
            "- If no value or 0 is given, it means infinity")
    private Integer soldQuantityLe;

    @ApiModelProperty(value = "The list of product pool IDs.\n" +
            "A product pool is edited by the operations, corresponding to a bunch of product IDs.\n" +
            "The product pool IDs will be provided by the operations offline.\n" +
            "\n" +
            "Notice: Currently, if no title_keyword is given, only 1 pool_id is supported. So in this situation, the length of this field should be 1, otherwise we will only use the first pool.")
    private List<String> poolIds;

    @ApiModelProperty(value = "The type of sort we applied to the result. Currently, there are 6 types:\n" +
            "1. \"RECOMMENDED\" Follow the algorithm recommended order\n" +
            "2. \"BEST_SELLERS\" Sort by historical sold numbers from high to low\n" +
            "3. \"LOW_PRICE\" Sort by price from low to high\n" +
            "4. \"HIGH_PRICE\" Sort by price from high to low\n" +
            "5. \"NEWLY_RELEASED\" Sort by the product edition time from late to early\n" +
            "6. \"HIGH_COMMISSIOM_RATE\" Sort by commission rate from high to low\n" +
            "If no value is given, we will follow algorithm recommended order, namely the same as \"RECOMMENDED\"")
    private String sortType = "RECOMMENDED";
}
